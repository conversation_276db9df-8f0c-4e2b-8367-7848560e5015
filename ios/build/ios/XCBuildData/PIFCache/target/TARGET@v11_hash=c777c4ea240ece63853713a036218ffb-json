{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7f2a23379b268ef71e542d270f9c565", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d4333f5a25c04d2dbfceb3c37573a460", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9867f432d8267743aab02b83be71a83a17", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e93f2e9293a16aefe681d4641a1a199", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9867f432d8267743aab02b83be71a83a17", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98015effbd8f5535d48e2a29404c652512", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985f456051a1e80830d4150bbfd1c70c6b", "guid": "bfdfe7dc352907fc980b868725387e987aee3d2f8ebd1ee2d60e9282899ee964"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c980f4b541311bceb741cb577e8173b7", "guid": "bfdfe7dc352907fc980b868725387e983b19a3ac68b16a82618d82361d4006a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fc1ac68b03a1e6aa57ff94d2f6040c", "guid": "bfdfe7dc352907fc980b868725387e989723f26ddc06e693c9e205da7f68fb8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84def3870977355c0e5611b55590c1d", "guid": "bfdfe7dc352907fc980b868725387e98cdefa682f412f79eba02319827711b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1f7e16a729a05c9a86d1dcb39c0cb99", "guid": "bfdfe7dc352907fc980b868725387e98a076a191cefbc1f3994a1f82affaee18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98903a769557be56776f06404c6bc55ac9", "guid": "bfdfe7dc352907fc980b868725387e983222ae5523e253308c0c1ec8c7ded0c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bb33d2fabd06d83a3eb31a94c4993a2", "guid": "bfdfe7dc352907fc980b868725387e985828591e8c75b598518aa5a5989d6956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801bacaf3920213a4915be4bd5b89c6e6", "guid": "bfdfe7dc352907fc980b868725387e9857e79040d5fca827c3eaedf890cc3625"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864afec203c32a0fedf3e2d1fdcd15bad", "guid": "bfdfe7dc352907fc980b868725387e980c5723439f0bcde08e63516eca3301a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e738b04051179321699a10e34c02a2b0", "guid": "bfdfe7dc352907fc980b868725387e98427ecfc1815a0c16ad5d5140ade1e25b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f313e889667aec198c1af5a77d15854", "guid": "bfdfe7dc352907fc980b868725387e98a8aa6a9892f8df161d86b93d1334e0e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823641969e2ff0f36f539a9c21404bdfa", "guid": "bfdfe7dc352907fc980b868725387e98524bfd7d331254ab0ff2af1d127d2aa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982be849a834df6dad09e9d4ec80577ee4", "guid": "bfdfe7dc352907fc980b868725387e98d96d7be060ca7d84bd09ed6589dc0b14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827feed16d53ebc141ba3d98db8d4bef7", "guid": "bfdfe7dc352907fc980b868725387e98c218337ffa147a575a154b3b4f107746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823e994993b5219d3f7481de20a109fd4", "guid": "bfdfe7dc352907fc980b868725387e98b5c47aa7932aab67a17c5ac29ad32f5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e59ddb4bd8cce8f59847e69ceb05b70", "guid": "bfdfe7dc352907fc980b868725387e98432b9c37790ab85c28e8eb8e55083510"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b973d0a8d4152db216ad8888a34f1940", "guid": "bfdfe7dc352907fc980b868725387e98215bee02d5f73fe757157cafb24eb114", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d98c2b1fb83a922b4630d7195ce65db", "guid": "bfdfe7dc352907fc980b868725387e980cb62b648b069059732fb7201e87689e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa215be4f05648ead4ad1ed069ad4d3e", "guid": "bfdfe7dc352907fc980b868725387e9835d60f624a02117095121f8464b6f4b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b494d6c55ba408d67fa040e6253ee8", "guid": "bfdfe7dc352907fc980b868725387e984aae66b1b8afb50e12e2197db202c239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b43dff211b0b8cd47b68a799a8bd23", "guid": "bfdfe7dc352907fc980b868725387e98de4d1f8e686c135a9dfae9390ab834d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986285a3537e4de731eb043ab09d0b3aa1", "guid": "bfdfe7dc352907fc980b868725387e98a4a9529dd9e77c903c41359684b758ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd0eef7b188f98cc9046ff4fbb4e9ee0", "guid": "bfdfe7dc352907fc980b868725387e9833f5acdfdd2e47c84bcbbec3e1df9480"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c26e9d89c31fd868fa989f5b54429b5", "guid": "bfdfe7dc352907fc980b868725387e9888e4ca0e3cec3aadfe2650b3ed8a6a4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa9ca07e92fdc3f0f7f0df43bdcafd8", "guid": "bfdfe7dc352907fc980b868725387e98f1cf365ba6c86ce70877fe4780c7ea56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988535c5b5111a9bc92f3b0144cdf8a2a4", "guid": "bfdfe7dc352907fc980b868725387e980d684b60250eeeb81e4f506062e84cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc1bf66f505baf272cbd53faf281df7c", "guid": "bfdfe7dc352907fc980b868725387e984dab3304cebef9c8be650e17537c7c44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b13f5b3b83ea6e08c141a4866213ef7", "guid": "bfdfe7dc352907fc980b868725387e98242482b93068522221cb4c8d9a463519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec10b7ca68f89987a7bd2fccefa6c9d3", "guid": "bfdfe7dc352907fc980b868725387e98203546d4544bac2a9f3c954f2867d6d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850f56262462ea8772db33f14a882766f", "guid": "bfdfe7dc352907fc980b868725387e9833da1abf3a486852336afacfd0ac50ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3d95dd5233446bb1fd4f192e19dece", "guid": "bfdfe7dc352907fc980b868725387e9867670cb5f7c4deec6f2ddc24e3cdfd36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982761ea120c395c3c41db866ec1e2f70a", "guid": "bfdfe7dc352907fc980b868725387e9872f82c8d4cfae24f80bf061e57fc0a37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde282c47f52f39456d949e6be720ccf", "guid": "bfdfe7dc352907fc980b868725387e9808d9987027c35d3b98331750e3ca0f16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5bc913677a60bf9e25b55383ebbc3a0", "guid": "bfdfe7dc352907fc980b868725387e98b878d6eb38a0b70e9eb29b1208a1e3d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af88200e8a7b1e5493f2dc4dbeab93fa", "guid": "bfdfe7dc352907fc980b868725387e98ca7416fe9edd2224abf9a113cd0e79ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c979b888e80302cd30498e8e10ca6568", "guid": "bfdfe7dc352907fc980b868725387e98b6c76a7151222ba136b6c36873827499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2ab143fb1f6b1951f68b20713f56e9c", "guid": "bfdfe7dc352907fc980b868725387e98717a4e94d4cc349f6bdfd3fa53e8b66f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d13d4ff6907228080598042a44ea29e6", "guid": "bfdfe7dc352907fc980b868725387e98cabd190e390af595749eb2dc285694b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986114b6eea8e1c7a9f71a394d0a21b6f0", "guid": "bfdfe7dc352907fc980b868725387e981caae49c0f292a06a201f9236f7d604e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84e4544b41f44457a47c6d083d677ab", "guid": "bfdfe7dc352907fc980b868725387e98bd2a2c3528af6249e623a4afc0b95a78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbccd822b1ab9174d1e568dba8468d9", "guid": "bfdfe7dc352907fc980b868725387e98129c267f15e9028b5a653c838dd234c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864cf48c4a9faef720a4577c5f941da4a", "guid": "bfdfe7dc352907fc980b868725387e98b7abd4eb2223da902178d1bcf6ce7f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986863ad93abbb966e35d381fc5ee0d3e4", "guid": "bfdfe7dc352907fc980b868725387e980f5712dd3a001aa13014caa4f2aa33b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0b83396cd1646c95ebf7a79314e970", "guid": "bfdfe7dc352907fc980b868725387e984623f891a0c5c078d5f9f9db6a5e130d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a570d1b7f01abda80936d5ecefede8", "guid": "bfdfe7dc352907fc980b868725387e98fd04d72b19c9e9643554aa96ae950825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043bf8ab3655ead09c3cfdbb0d3a6fdd", "guid": "bfdfe7dc352907fc980b868725387e989f612d6594f72e592fb51dc8d91c79ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd764ebdb01b45deed3310106b39823", "guid": "bfdfe7dc352907fc980b868725387e980986cabf22166d942b6e6f4e2d80b4b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980533461a531e1f83ef4e839927766981", "guid": "bfdfe7dc352907fc980b868725387e988f8374d7bbcdbabe9c6937d30123908a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847d9fe483f9eec32b88078914b31f2ce", "guid": "bfdfe7dc352907fc980b868725387e98427edf6ddf046febc89f395cb72fd7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837b423e1ef640bab281de9b38773b6be", "guid": "bfdfe7dc352907fc980b868725387e9885a418c76e4edad1b16b027866a93475"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74dfa5c2b27b6b66deab408437bc0fb", "guid": "bfdfe7dc352907fc980b868725387e98fac5afcfec805f22835a4cce820bfb94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b5491175495218a44de1f6f4967358", "guid": "bfdfe7dc352907fc980b868725387e98bc88858297346436cd522353195a6c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ac7cc0357e71554f7ebc72be889b7d", "guid": "bfdfe7dc352907fc980b868725387e989078e9de8f01914b832f86c292cfa5f5"}], "guid": "bfdfe7dc352907fc980b868725387e9898784d7898f849afa858e7508a4525a9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838c8338d605a957fac7f1c411a707720", "guid": "bfdfe7dc352907fc980b868725387e986400c98e47f0ba03e0550853ac1242e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae744aed32e6cf1b294563a02580c2f", "guid": "bfdfe7dc352907fc980b868725387e9823d74223252003ce829ccbda53ae1d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c23021c50cdd2324e0fb4f76078d84d", "guid": "bfdfe7dc352907fc980b868725387e980238c3d8b2faa760c585998ac04f67e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861c551b985f56ddbc51b083ce1116aeb", "guid": "bfdfe7dc352907fc980b868725387e98ae32c9b19dd11f297067ffc874c39be6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a1f93a07b4ac3523e80e5dbbaeee586", "guid": "bfdfe7dc352907fc980b868725387e98deeb2a384c08129d3da1febb8e9f4207"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f6c5258315349a557ece50f74e01499", "guid": "bfdfe7dc352907fc980b868725387e9834c6bca8c50a44067c6b058ea9b4fe4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8b817fdffa4552bf993bda01e7793ff", "guid": "bfdfe7dc352907fc980b868725387e983a8d4565b6ec7a1b78b89d2972bdd67a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee3b37eab9583161096f16d3bb9eb079", "guid": "bfdfe7dc352907fc980b868725387e98c708f63f0827fae454113ba5ea38e21a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866888cef55998332a9c9ec23aceebfc5", "guid": "bfdfe7dc352907fc980b868725387e98aa9e370efbf7ab9e835a783bf13cfed5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff2dcddce510b172e30a5481920f493", "guid": "bfdfe7dc352907fc980b868725387e98841774555e509907120986b83921265d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31c1e768a7cda2de842824689904c90", "guid": "bfdfe7dc352907fc980b868725387e985981bd97d63499a60ff5ecaa23977890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ee1f890424dba27208916ae34ea180b", "guid": "bfdfe7dc352907fc980b868725387e98747ee047551f7f874632d2b97eacb8ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f14ef68a6c078f7c90777b2b7158adc", "guid": "bfdfe7dc352907fc980b868725387e986b26001fa74d6d98f285d36f72e946be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b81052dd7fa4b5153a86b66dfc256f", "guid": "bfdfe7dc352907fc980b868725387e9889974dae8e25aff32612906285a4c08e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c8822f52fe79d52b10e122905b394e", "guid": "bfdfe7dc352907fc980b868725387e984ff07153c03e446936d9778a0a8ebade"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98256bf92e7c766c2a8ed8223d6be685af", "guid": "bfdfe7dc352907fc980b868725387e98561be191e2070ba2ba7e7a176503d94b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb47eb86161d77b87cb42413242668cd", "guid": "bfdfe7dc352907fc980b868725387e9844b7474c3857c54efb2be3aac68360f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f1a8bb1b10592cac1df8d3cd4c12d94", "guid": "bfdfe7dc352907fc980b868725387e981294e943ffb0ecf2dcbf4511b7239775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c2c1c222e5dc4c0577087f2cdf8690", "guid": "bfdfe7dc352907fc980b868725387e98152469e316662e796b4705bb1030ad4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e763ce5a4da8103081c3a4e5b7a265c", "guid": "bfdfe7dc352907fc980b868725387e989bd992fd426752b63e638d868e784c1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886cd7d946b1d93d9ad79bf0a7b4b547e", "guid": "bfdfe7dc352907fc980b868725387e9891167f9b6c974b48aa3edf0ddffa238c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983930d6411063dabd28e3841d555c85c6", "guid": "bfdfe7dc352907fc980b868725387e98e45c30818118d68f12ad0864273e1c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebd23b6984d3c7d39cf8145981077210", "guid": "bfdfe7dc352907fc980b868725387e98b757aa0f087d0c9e5da9e4c6a610f124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98654a9689802dbea77fe53044319a3dd3", "guid": "bfdfe7dc352907fc980b868725387e987788e2ceb89580194cc9944aa1a12d1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3410d1893501e598a08330700cdcbe6", "guid": "bfdfe7dc352907fc980b868725387e98784bc0bf3a0a7dfcb7c64dbb2b5f1ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5c79938c134db5d61fb5687f1bfabc", "guid": "bfdfe7dc352907fc980b868725387e985f1f8ab99ff00fa6be855741ce2e89dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847752ef4c6cc63850c3eeec04118a419", "guid": "bfdfe7dc352907fc980b868725387e981b93ff0b679f64d9b94fecf15a2bc5cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7763bf8fb198b95a751ed7f3b8dcc97", "guid": "bfdfe7dc352907fc980b868725387e984ab741afba42cf547790c7f0c4876c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807b1f619fd6e2a182d676825391e90dc", "guid": "bfdfe7dc352907fc980b868725387e986135c9cf658acc7f939fbcd5ce878914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193a302485bcc0fbc00cde3e67cfa1a8", "guid": "bfdfe7dc352907fc980b868725387e9826093cb1b826d400ca68761970acc75b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987954a8202639a30e7d8f8089e705692d", "guid": "bfdfe7dc352907fc980b868725387e983fcb15827ef3d9577ff9726fd48f021f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a12238bd5b9ef0a970ae98720ffd923", "guid": "bfdfe7dc352907fc980b868725387e984b8c626e09f1f9d1f8f37162fa00278d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da94e096a40a4fc28dec81a718d5bb23", "guid": "bfdfe7dc352907fc980b868725387e98ac99b56efef1362b9901526d7b77e6ba"}], "guid": "bfdfe7dc352907fc980b868725387e9858787f632f8d452cf1d7a82924c15802", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e9863b2a61efc2ee6a610dffd1befe410d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3dc52c9b90b9555e28483e47b792ae", "guid": "bfdfe7dc352907fc980b868725387e9806a01d03f357e54764b9eaae7bffa553"}], "guid": "bfdfe7dc352907fc980b868725387e98b43f6ea5b26f97330de2e064f2585ec5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982aa238eb7567faadfcfc614b11a2a840", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e983d8821d40fa0aeaa4140a245b2bc936f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}