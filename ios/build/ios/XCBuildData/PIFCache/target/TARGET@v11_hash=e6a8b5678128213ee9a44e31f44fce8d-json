{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980d435cd06864532d3405e40daa83a808", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd4eceddd73e04c4969f333f07327831", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab02cfae71c721717c69b61398929594", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8e36e47d864691e8fbf1acb99aa82d2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab02cfae71c721717c69b61398929594", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f2ee12efe2081e35245571421a6fefd0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9840b37fccc648f80348a678ce77206043", "guid": "bfdfe7dc352907fc980b868725387e98821b4cdaa332b6b0053255b5b8b2faec", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c49376409fee67ebf161c1ab1155ba10", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9854f53f989d1b614ba0c72a1fcf6e01bf", "guid": "bfdfe7dc352907fc980b868725387e9800ccc0acef982f397ba394fe9766ed14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2f08c1c5dae9ba9d808a7587e42225", "guid": "bfdfe7dc352907fc980b868725387e98d43a947ee05e0cb9c4f1cab7d775863f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073bf2c45ba78612395c0bd19777ff9a", "guid": "bfdfe7dc352907fc980b868725387e9882fddb13433c8298446b4cb7bf5f5225"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f137d378eeeb91ceb8a15abdb6254a6", "guid": "bfdfe7dc352907fc980b868725387e984ae728603fe896e06d813e9cdb06673f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98262cc3dd93d9b0baad13d3f4b02704dc", "guid": "bfdfe7dc352907fc980b868725387e980ab09f674aaa501dbbaac35d2d904705"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af9c14e8de1bbd3f9b69adb82afc687", "guid": "bfdfe7dc352907fc980b868725387e98758192fd09e40b9fafeccda6b1ba76f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836afdb12ac99f78637f0c6415f806131", "guid": "bfdfe7dc352907fc980b868725387e98c4f76cfd2db7557f000f83f710c1d461"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855794b21195a331691fc32f9c4abcc3b", "guid": "bfdfe7dc352907fc980b868725387e982d25c2abc1bc305ffa8dce7abca30a89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98285a4623df2f1d1f7006ad28e6d6f5f3", "guid": "bfdfe7dc352907fc980b868725387e982515cdbb4c96dbe17756b1f3a0704d00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a4fc16926a431dda22202debfadc52", "guid": "bfdfe7dc352907fc980b868725387e981323140868cef6baa26ae844d9c7da5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44436deb5048b94993ccb415c191881", "guid": "bfdfe7dc352907fc980b868725387e98df7514ee9da52c3e858f17b4d04b4a94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d2caf7d72b185723f869599a4dbaeae", "guid": "bfdfe7dc352907fc980b868725387e98bc373b5092cd69157511946fd03966d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e1d4303a147f3b92964ccd3f1cc7856", "guid": "bfdfe7dc352907fc980b868725387e98f3b57828eb2f3d754ec7943302b2b262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877bf86b38bd9f8dd8d10e9eceee7260f", "guid": "bfdfe7dc352907fc980b868725387e98625d9ab7dab65d8a8e5f946147d5c119"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a25472b92d67605aa1315610c6de8581", "guid": "bfdfe7dc352907fc980b868725387e98af225a618f7752338b6891d067207de3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12635bb753a110c0bcf592a3b21a313", "guid": "bfdfe7dc352907fc980b868725387e98ced6f0fc3ccc2531dc386e0341808a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe831d48ef02582327c831372b62579f", "guid": "bfdfe7dc352907fc980b868725387e98c9c75314b6ee3dc0609054dc226a516c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a174e9a8240fc18383f256539bb700", "guid": "bfdfe7dc352907fc980b868725387e98c4f2fe4c106efc645331a69600eb776c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2232949aaf8845f677131f27e96438c", "guid": "bfdfe7dc352907fc980b868725387e98837cac4624c87235b072dc2f610db0eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b04d6e4c78e6c2de50785829e983ff4a", "guid": "bfdfe7dc352907fc980b868725387e9877c42be75b8daa241f95d885ecb942d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d580f0eb9720c37fb94848ace9758a7b", "guid": "bfdfe7dc352907fc980b868725387e987a2d0d71cbfb4acf3e75ebb956d4f9b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c515f10166f818f01423194544f16e3b", "guid": "bfdfe7dc352907fc980b868725387e985d643767ba24ccfc5c17687f779f99b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6fa88b06bf0cdb5a38744744dc254e1", "guid": "bfdfe7dc352907fc980b868725387e982117e17891da593b9f7e6b013c23ceaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c11a68012bbf75037aba643c33e9765a", "guid": "bfdfe7dc352907fc980b868725387e989491d69294a7f453bb5bb6cbd95a627a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987217b792d98b9715f9382db904b12927", "guid": "bfdfe7dc352907fc980b868725387e98c414ce6a7bcf15e103c8f7e5006792bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98063b0a0faad7bb87d8a4376dc7751723", "guid": "bfdfe7dc352907fc980b868725387e98ea891fa4234d34a1fe7cdba20af13f3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e34c7c07c7ce44776f08ee450e052552", "guid": "bfdfe7dc352907fc980b868725387e988d8d00c416f269f833cd91e0ee3f0550"}], "guid": "bfdfe7dc352907fc980b868725387e98f021694cacb27b15fec666e2d3e8201f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9895e83a650be86b53936346ad8fd2b7c5", "guid": "bfdfe7dc352907fc980b868725387e9894604586d588919932cf75d55c7d623e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0316b1ab48eb16cc1080a47c3f281f4", "guid": "bfdfe7dc352907fc980b868725387e981fcce31db0d344ba82759378a4dbed66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e989f98e20ced406fabffb9f1c331d618ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ddaade9056ee0fc09f17fc9a6103e1", "guid": "bfdfe7dc352907fc980b868725387e9898c1a3eb3f77837e192b7282b36ae05e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5fae1b72688673487ee184fe608cef", "guid": "bfdfe7dc352907fc980b868725387e980fe88c8588149183275208b4465d03a9"}], "guid": "bfdfe7dc352907fc980b868725387e98721284ec7be8f9034a85dd065b7c3cf6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985d129fedd6300f5c0dc8ff691ba953d7", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e989262825a1ebf0065ddec01db12912905", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}