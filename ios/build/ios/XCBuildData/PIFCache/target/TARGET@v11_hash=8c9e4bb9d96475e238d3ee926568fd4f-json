{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd7ef14226d3e9a9b46084baef2a61a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816f9040bd3d9cbe17a95347fc396a826", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4dfed6e17020adb3b9ffe60d4cac784", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982056c25919b08e23be104e325305b096", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4dfed6e17020adb3b9ffe60d4cac784", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6672e0aa63a7c50f6d2144d4b8eace2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9809e5b1d1dce2cb0d6c31f9f3414d7328", "guid": "bfdfe7dc352907fc980b868725387e98cc05abab23fd93ded94c671faba8c953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98156903f2b8e136fcd43caef101a00e58", "guid": "bfdfe7dc352907fc980b868725387e9866aaac62dad7d94f60f48666eeb8c517"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab11ad1963fc1b9a9037899c243772a8", "guid": "bfdfe7dc352907fc980b868725387e982dc2ddbdd98be1d7d1dc7564f3a90410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895459b8b1e166402ca9503433c09be4b", "guid": "bfdfe7dc352907fc980b868725387e984370699f9fa86dc972ebc32e9059e4a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbc173b26162f39d05f2585957818c45", "guid": "bfdfe7dc352907fc980b868725387e98633cf8bd505c457f0dbf8751aa684356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ece0d36ab9b6e50c6913db8f8f7ea483", "guid": "bfdfe7dc352907fc980b868725387e98a6d3bfaf1b4b95f837f4bd8aa5049de9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865e7968281a83e3ea4d820822a15232", "guid": "bfdfe7dc352907fc980b868725387e9871adb3fac93420d4fb26ea739ecc67db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fac10a23e66da5bd6a51c03e779eeca", "guid": "bfdfe7dc352907fc980b868725387e98f7bc5554ad37c3fc778e2b9c2f9793d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823b9e2f958d541ded6c9c1addb6f07eb", "guid": "bfdfe7dc352907fc980b868725387e98ba45d270d3ed1473b600778dc357719c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815b48aa47921c023d4d1ea6a7a5dbc11", "guid": "bfdfe7dc352907fc980b868725387e989171541b365ebaccffa8660cf11d06e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48bd1bf4d823aea61aa8ae9f6afea8b", "guid": "bfdfe7dc352907fc980b868725387e98f2a36b453c3e33551cac57e009166bfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c1eef03faa114fd0a5be7126914971c", "guid": "bfdfe7dc352907fc980b868725387e9883e40a27aeb92a41db64224a09336fb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819abe198706f1809e61defda609e4e89", "guid": "bfdfe7dc352907fc980b868725387e98ee6248910970836abac8e870177678c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc409fa00dad8cd86528af5f80d308a4", "guid": "bfdfe7dc352907fc980b868725387e98d610087b353373d872b94a3d8adc7806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb0895db2fc6c81adf8df29d3c7b57b", "guid": "bfdfe7dc352907fc980b868725387e98ad889aa3caa6be7a06920b7774299770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ca75df83f3d89866f84853bf81b339", "guid": "bfdfe7dc352907fc980b868725387e980e7263fdf805dfcc615435619bd134c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981004674dc48a465229b0eff8cfde9e45", "guid": "bfdfe7dc352907fc980b868725387e98425b2608ad463d66aed503f975af60b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebddd324a6509700ff765a87ba888b47", "guid": "bfdfe7dc352907fc980b868725387e984d01b1c5f3f32c15ffedf106d7dd15d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4f57de7ea7ac414af6e9ee481ae69f7", "guid": "bfdfe7dc352907fc980b868725387e98501700c67c81d6802d94803df435c92f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987295eb57ffcf527c5db8220e9752d23c", "guid": "bfdfe7dc352907fc980b868725387e98703fe5a1ed35b3dac4d1d2b3686dab67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc7fc614e0a2ff8b34aa67d0253b903", "guid": "bfdfe7dc352907fc980b868725387e98ae2cfe2b2fb7bb2b3604f7dddf8952aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a4315fc71ce1719edf2fc64b32018e6", "guid": "bfdfe7dc352907fc980b868725387e98c420dc9f8a835bc0c90085470560981e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53d2ce0ab14a2c91c90aa23011c2578", "guid": "bfdfe7dc352907fc980b868725387e986dd69932e8ce38a71d75a314c98c3fe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94290c642e80bfdd7f85c342928b01e", "guid": "bfdfe7dc352907fc980b868725387e98faa7ac019815905fcdd04fa1f9359bf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df5c2cc507526e8975f6a8952c8a66a7", "guid": "bfdfe7dc352907fc980b868725387e98bda9e22c055dbcb91df15d07716c98a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988581f20b8d89b842c1b4a77b9380c04a", "guid": "bfdfe7dc352907fc980b868725387e98e3493d85d2908ad16be2cc114bef6b7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ab07b7f7031cea175a95c460789fa9", "guid": "bfdfe7dc352907fc980b868725387e9846ff8f37491048e37cc092b15a4d1af2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fcccf56efa5cb5375272b52b5cac484", "guid": "bfdfe7dc352907fc980b868725387e9817464443ce7acba2f04eb1ef6963afba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d21f080bc590e925cc4fd0c9fc67dab", "guid": "bfdfe7dc352907fc980b868725387e987a02b32ff0cfef87d55d5dbf574f3036"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864e87df4747c2cf6f7faeb58ef36ab61", "guid": "bfdfe7dc352907fc980b868725387e984e5662549820d8b95cf0c337a6af9c35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc1bbd812b05202c8f44b4e39eecffc3", "guid": "bfdfe7dc352907fc980b868725387e9888e3f7bab3ffcaa4d72c0c0415bf00bd"}], "guid": "bfdfe7dc352907fc980b868725387e985ca2dc7e46eaf0e941186507c110a0bb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dbd90d52564baf7e78f1ed62954565dc", "guid": "bfdfe7dc352907fc980b868725387e9840de8a0da13dd25cd990d04a2d90081f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98319a629f1380ae8da8c5ecc5185c9635", "guid": "bfdfe7dc352907fc980b868725387e9822d9ed40c03dd3975f29472daab4d418"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98867a1e3dc7f8a2968d9f29987adf36ef", "guid": "bfdfe7dc352907fc980b868725387e9878e0925b4b9aead79470bf5dfdbbd0f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f6299e2ae27e516c3f597dbf70f191d", "guid": "bfdfe7dc352907fc980b868725387e98e5494138482b31b200ac94a368656ad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988777689ad4da2e16495e57e60208202d", "guid": "bfdfe7dc352907fc980b868725387e98bbbbbba53c373d272d017fb9ca960622"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c425f313bccd71c165fd7b749ba0d0e", "guid": "bfdfe7dc352907fc980b868725387e98f10b1c5af34ad746874584566463bee6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e7bcfccec51ba6acd593a7cdb58260", "guid": "bfdfe7dc352907fc980b868725387e98858568f0ed3048cf3440a08168a51c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980789c71c1a5c557019eb12c713b393d2", "guid": "bfdfe7dc352907fc980b868725387e984cbd7d0f406f755e57b19315d4b43aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea422cbf252da1a4951e6b72e7969a70", "guid": "bfdfe7dc352907fc980b868725387e98de956e5f2289d684727732492a64ba89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfb6cd329866981d7fed06b52be624b2", "guid": "bfdfe7dc352907fc980b868725387e98d132e23d51c1d77f66484d5429fa33ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982979e96891609700a3848ef24c407153", "guid": "bfdfe7dc352907fc980b868725387e986b3b5eb1e5ba0f5af94f523a5dce339f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceaa7366c4febd020ac26008da424b30", "guid": "bfdfe7dc352907fc980b868725387e98a7e7d1a4aeb3e28f3a19d9f4aae2a4c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e68b1bba9aa8cc3eb8ebcacf47c3f8e6", "guid": "bfdfe7dc352907fc980b868725387e982cf62faf27d11b3159df38b3ef81f747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988943c9d14759fd114785bb320c4f1678", "guid": "bfdfe7dc352907fc980b868725387e9880d2ac7321e700fc4bae85c4a2f1b1c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176e62fede65787b5f83593d50ebfc93", "guid": "bfdfe7dc352907fc980b868725387e98511d7756c40f07e5f210a3145f6ceaf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cee9af6ede8df056403ff2fe328f892", "guid": "bfdfe7dc352907fc980b868725387e985f712707df755389f728029879e099fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c178b7648eaedb7939b05f76e7761b99", "guid": "bfdfe7dc352907fc980b868725387e98aa40fc6b1a7afcfaf2f19d10aeba33fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835a7f7d09883f49a4d2a372181293aa2", "guid": "bfdfe7dc352907fc980b868725387e9834e2270ebc31fb7351ac00e3a571c971"}], "guid": "bfdfe7dc352907fc980b868725387e987a82510c55a17ac41b6164d9878be6e7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98b19a974a0ad4ac1f84d118332648c876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9187d35bfda3f41ff6bc7539a79d0", "guid": "bfdfe7dc352907fc980b868725387e98a027de14376c164ed3d5b0ba29d1625f"}], "guid": "bfdfe7dc352907fc980b868725387e9849e96873dd644d768dfcd7d0419e721d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987b7e587c46cd827cefdcf4d9803fd7ff", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98f557df5ec60eb6f955ee4aa811e519d8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}