{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980eb509e748eaa3f6822a52ff46f3a248", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e1c92aaddf03b9bbbbd8edd8b2ed9249", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897af29b2e9e22631e73a37cc29700c1e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9850c5d83ae6172a49a728e37b9f87ae85", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897af29b2e9e22631e73a37cc29700c1e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9812a4d0680d293c3c3acfee7030de3c7c", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98eeea9f5bdf8d960a10aacad2a94298f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986463400edd220573225b9721c9d7343c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980fd584bf4ac867601b5571bcfe07348e", "guid": "bfdfe7dc352907fc980b868725387e98f64c69b8295d813a7faee64c6d45b63e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98835546b1104b2181f233561ce1c77a7a", "guid": "bfdfe7dc352907fc980b868725387e98caa709772b0a135d88eb7f2f16a00e0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a311788e6350e7765f4a0d566a9e92ca", "guid": "bfdfe7dc352907fc980b868725387e98015135ac0b4715772e16b51b2d31a712"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51e1db81968105b90724888811f724f", "guid": "bfdfe7dc352907fc980b868725387e980a3bc009de14cd8cf8694e76609fbd84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983827e2fb1caf2143ef93e4ae9545dbc7", "guid": "bfdfe7dc352907fc980b868725387e9859c05a27a93c46d054afe753a02c9d7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842bf0bfbe6d6cca0d9c1c918fb53d9a6", "guid": "bfdfe7dc352907fc980b868725387e98d2d5d785e920a57d10a87819e3bc25a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab129a6abb81d8672a3ebdc5d06d3e3b", "guid": "bfdfe7dc352907fc980b868725387e98e88b80bb6cec41e8e86e9a3d519f7125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ddc336240922fbc16a484c5ab73ede", "guid": "bfdfe7dc352907fc980b868725387e984a25650f34d8b5a571760983446ae257"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818ff05731503bf88868a5cb11a761d18", "guid": "bfdfe7dc352907fc980b868725387e986d887ceece9b10bc8070d050a2ba572e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2b5b7f251e35eca1d71d5830860f12", "guid": "bfdfe7dc352907fc980b868725387e9881017730b3ff66f9182b2f99bcdf2826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836856b9e127c92393142ecd533820b27", "guid": "bfdfe7dc352907fc980b868725387e9879793ad5dd85faf7ab4ed99b87f707d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca3219990ecc53cc434bfc215c6a4cd", "guid": "bfdfe7dc352907fc980b868725387e9894a0ddc1d0e82c09368b92b48e8112cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533d5c35b67d9209ea52a43398bbc4a1", "guid": "bfdfe7dc352907fc980b868725387e98f86bccfcd307f0510b75ad696014ba6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7cfadf021e1da5a0ba4e09f1ee0adf2", "guid": "bfdfe7dc352907fc980b868725387e98d9a071456aa46d12fbb25e7b7ce1df65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ca70858f2968a0eb6aedf238a37db1", "guid": "bfdfe7dc352907fc980b868725387e9810dfdf06661e520841fa3676c6255428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc5fec34fcc8a86437be5ace72a9e13", "guid": "bfdfe7dc352907fc980b868725387e980240a2fee650df2667595906ad3e330f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f80a890f7c52dd03cf75536e8850d05", "guid": "bfdfe7dc352907fc980b868725387e98921bb4f1e804309683450bae621c0e32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53e1f094225776d8839e3a60aee9767", "guid": "bfdfe7dc352907fc980b868725387e98e9d643aee4e6da424caa9e35414ee0c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b20f7a32d914f43ec3155745d89c9578", "guid": "bfdfe7dc352907fc980b868725387e984bbb12e1c14dad8ff087c60045c75df3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a2cf434bda3e6303bdb577317c8e414", "guid": "bfdfe7dc352907fc980b868725387e9852bf17a5e6eb856bb7a1abb6731e9ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985791e7984ab607833abeed5421e21437", "guid": "bfdfe7dc352907fc980b868725387e98e652e32a33ecf955d31481b8238b228f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ee26fb668f9c8ebdbe4247b64a863a", "guid": "bfdfe7dc352907fc980b868725387e98442576ec0b68c57fbb3c904909af79a8"}], "guid": "bfdfe7dc352907fc980b868725387e98f3a70c3e0adc8a0c781be7070ce1d910", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}