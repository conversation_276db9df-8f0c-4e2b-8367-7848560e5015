{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1a7f1e9a07db739fa47e3c024147a74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988ab876c0a6c1f17ae59dcf9a3f549d73", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4a1fad1a11643de9118bf055bea29e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98952b8e2d41cebb6197c6767108579c03", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4a1fad1a11643de9118bf055bea29e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c064ae61298c86a34220e93303ae4e6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf0c1a374e231b470b95fafab40a46ba", "guid": "bfdfe7dc352907fc980b868725387e98c7a17214a6e436e9a34b9abde86155ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9555fca58c9219242dd0df96f6aa1e1", "guid": "bfdfe7dc352907fc980b868725387e9824edccff36d89055ac6fca3b07d4df8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e7d7cc4a0aa4af4a6c7b099c09a840e", "guid": "bfdfe7dc352907fc980b868725387e98613596c4c2000923cea03d223a3cd328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e6cbebe1e70fe91b46801ccd6105db7", "guid": "bfdfe7dc352907fc980b868725387e989cfdf7f284d5fcd068161761157911c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebe6629b52e9b654a41843ae3bd7522f", "guid": "bfdfe7dc352907fc980b868725387e987ead26176daa32c2c80b2ab98704cc18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d506b1e92997c8fb683143b01143da", "guid": "bfdfe7dc352907fc980b868725387e98eb45d1699fc039251e5ea0755ea6d622", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9835c917d071c987bc2c7e82b28aa6bcc5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980a29e80b82efe18a69f85641aa8bd28c", "guid": "bfdfe7dc352907fc980b868725387e98d14367c6d85b19baa0891a73dda44ddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa01b9cad99356f6abfbe5f010f59d0b", "guid": "bfdfe7dc352907fc980b868725387e9816242ba242cc984d036f03dc81db1101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988488f881785e69316326cf93426e978e", "guid": "bfdfe7dc352907fc980b868725387e98c5082dc4e40788293309b5619d611ea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb5e704b30be1577a37033c8c2e1e260", "guid": "bfdfe7dc352907fc980b868725387e98d9ef5b04c5d28c2e3a047b201b723d48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29bf42d7b28f932592c8d357aff126f", "guid": "bfdfe7dc352907fc980b868725387e985bc08ea795ce93faa3f013821b4f7198"}], "guid": "bfdfe7dc352907fc980b868725387e987827b4382becd2016af03a78a26000c1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98a043e6e731ab65f4f5a3dd14bf8544b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9187d35bfda3f41ff6bc7539a79d0", "guid": "bfdfe7dc352907fc980b868725387e98cd98fde8d3f8d41a8ebcf70c9aee1f41"}], "guid": "bfdfe7dc352907fc980b868725387e9873b11a39048a7e3d46e29125d7b1cb32", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a6ed433315ecb0f944c92ca32634b443", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e98bdc9832550390e4a89872c829b2b9df4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}