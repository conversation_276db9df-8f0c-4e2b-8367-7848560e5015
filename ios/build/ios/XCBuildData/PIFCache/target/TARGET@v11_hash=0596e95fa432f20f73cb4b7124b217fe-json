{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98480cd87b970aa87661ff47f9d7e4c17d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984207c4423c6acba39061d69a9670c02a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834f5d910d92867d12eb5c3d30409e48e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e1f44ad468a1eb374aca92dc8143f04", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834f5d910d92867d12eb5c3d30409e48e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bdee96fc2ad6bd27c1037f5192b25e8d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981993c3d59e44733ef55e9ebe6869817b", "guid": "bfdfe7dc352907fc980b868725387e980590cacb8d96befb9f0942610ac16ec5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb3c2f8c3cf8a0112c1a01b90a8babbc", "guid": "bfdfe7dc352907fc980b868725387e9898a7c6b0d9654b1aaaa22824fc779d06", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e33ed2f9eac5b5ec83d64ef6082b6f", "guid": "bfdfe7dc352907fc980b868725387e98c5a4289d94d671cad6894895ae28d216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6109f917854fb60bdb0c184271354ac", "guid": "bfdfe7dc352907fc980b868725387e983159ac78a9cd95826a842ccbfd548e16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf65a5cd63a7802a170a0be8f601d70", "guid": "bfdfe7dc352907fc980b868725387e9862e2a448e680cc8b244fe1fff5f30e56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846eec721231458c1f50a9601b40f12e0", "guid": "bfdfe7dc352907fc980b868725387e98dfe9e0980b03dd823bd254028eb1400f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847381ee071eb9f2f29f1ab47691efa79", "guid": "bfdfe7dc352907fc980b868725387e98055ed17447ff639c21a8af7295216ea0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876df698b1509a508e7ed57b729e26d93", "guid": "bfdfe7dc352907fc980b868725387e98ba71c874a7a7bb508cc53f5086508965", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855038f5b213dba3beb3ca8f150027f6e", "guid": "bfdfe7dc352907fc980b868725387e982528fb595c98348234e7b98466291f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b84a03ca628d1e87f42b1358c2fae52", "guid": "bfdfe7dc352907fc980b868725387e98fecb1d86857e3aca3c8ff1916d144d87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e19cf797329f95ec69c668718a60f32", "guid": "bfdfe7dc352907fc980b868725387e98808ea5e545c39215471fb0d542e7132a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0f7d1cb173ca73d874c3cd73f4a3a92", "guid": "bfdfe7dc352907fc980b868725387e981afcb79586d26d076f2f2bccf930bcb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862ed700ca01abf700ea3e3f9e44b2810", "guid": "bfdfe7dc352907fc980b868725387e98365b561659e54020040abfe0f9506537", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d04b8f7d55a6a0e045e1cfb30b16aee", "guid": "bfdfe7dc352907fc980b868725387e98f24777cdcb53d0caba51c914270eff1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867f83347532d5c8971be2904ec732310", "guid": "bfdfe7dc352907fc980b868725387e98729edc754f58c55de46e5a5b78d98548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca8d024f9e2760d612d2372906fcf43", "guid": "bfdfe7dc352907fc980b868725387e98fc6d638083a8b9ab13934d743f6db636", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0607b8b5a37e577c2b9763797ea558f", "guid": "bfdfe7dc352907fc980b868725387e9874fe854aa2752e621de6879dfb1df338", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895fcbea8fd84c595e902735ddd510e02", "guid": "bfdfe7dc352907fc980b868725387e98f3cf7e5459b5b3552fc563d04158d446", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5951fe125688ddfcc82ee900ea6da9f", "guid": "bfdfe7dc352907fc980b868725387e9819df9bb04aba5b39ea580b3840d09121", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd3e205027cfd792fa2834c1da96ab6", "guid": "bfdfe7dc352907fc980b868725387e980eb440fe582ddac18c722dca784a4ffa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985322d4731771756bc83dc2c569f02481", "guid": "bfdfe7dc352907fc980b868725387e98ae42437d1fa38dc90c8977d99e943873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855872dcb8a86fccdb2386176187e44a4", "guid": "bfdfe7dc352907fc980b868725387e98d5700b2d56879acd820d38755f133263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d3c7db206f2054df8cabcdd6ae7d65b", "guid": "bfdfe7dc352907fc980b868725387e98fc91d17a0b6368fa31c62057c3138ef3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98971f3d6635f047ad3d85dae5b510422e", "guid": "bfdfe7dc352907fc980b868725387e98b6cab24c5be8cacd85b21640a977252e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b31f16ec2ac93c928fc8df05b851216", "guid": "bfdfe7dc352907fc980b868725387e98d28e43224c3cd230f624168acbca7966", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983776918b08fe0212686c3c6856803d44", "guid": "bfdfe7dc352907fc980b868725387e983248a4a272c9f9ab896a25946cef2102"}], "guid": "bfdfe7dc352907fc980b868725387e98783a9d23cc7b3a5a3de3430f8f26e982", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98588a414c4eb6f64c2f0723c6c6faed8e", "guid": "bfdfe7dc352907fc980b868725387e980640e4f708175737444a06fe991a4649"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c7b35eadc6e2a0ed846c3c03e3cc71", "guid": "bfdfe7dc352907fc980b868725387e987f2143b205e56645cc04a714a5258cb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987447a19c0effcc1b4f1dcc91ef3ae5bd", "guid": "bfdfe7dc352907fc980b868725387e98186364972cc8dbaac5efc9704e704e5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef797f922df09f9e6f479815850e26b0", "guid": "bfdfe7dc352907fc980b868725387e98b4724d53e20eb9be3afb40cbb9b9a77c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de6a935385dd8df93f24185441f74af", "guid": "bfdfe7dc352907fc980b868725387e988c1b609d9c81f9c4ed8096454de527e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de6fafdcd131a54a8e6d5fb58907cd4", "guid": "bfdfe7dc352907fc980b868725387e985bf6255e421a4ad294c4b8b23541babb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868d6f6a67cc6f685d88505a5ac125ab9", "guid": "bfdfe7dc352907fc980b868725387e9889665d2c4c00367d64aeb862928eb813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985621bad22e3a3ee78414296f7d213fb2", "guid": "bfdfe7dc352907fc980b868725387e98b1b8bcb30eaab61e6fc775bea87c57cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98208acafec35f8a67a953fdc84a0b7e2d", "guid": "bfdfe7dc352907fc980b868725387e9866f3914976bc05588c23628b521b1954"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cecf76f397369a6298aff806e79a6b59", "guid": "bfdfe7dc352907fc980b868725387e989399cf2fecf90d0e1649edd279dbae0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e5000fa7f72552be819526b5dccff3", "guid": "bfdfe7dc352907fc980b868725387e982df9583235b131e21bc39b572e5aef3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e2e575d36d3ca963ef162b25289a3f", "guid": "bfdfe7dc352907fc980b868725387e98e4d0629bc20e7a0ed74ede480c6f19bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98377af536972f937887c3cc71bca2c13a", "guid": "bfdfe7dc352907fc980b868725387e9832aca9d1a76e7ae36f8ae43ee001444f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98944170411f75f553894ce2c248887f5d", "guid": "bfdfe7dc352907fc980b868725387e9809acdd0d9940743793ebf13600d84cc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edbbc7f83a5237d905d8e7b7e4e1a32", "guid": "bfdfe7dc352907fc980b868725387e985443700471f56c497a2325164e7f7289"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d179495147cf678912a4945008f2266", "guid": "bfdfe7dc352907fc980b868725387e986363c4daad223531b83d4f64cebe220b"}], "guid": "bfdfe7dc352907fc980b868725387e98dfd9db784c31a6b29d39f8302317f1f6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98c77cd425e051b19534fdf9127a8b9273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9187d35bfda3f41ff6bc7539a79d0", "guid": "bfdfe7dc352907fc980b868725387e9806eb59e61d928af4df08a5da8d19501b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3dc52c9b90b9555e28483e47b792ae", "guid": "bfdfe7dc352907fc980b868725387e98378d65b540dee807b6b44cc139897f2d"}], "guid": "bfdfe7dc352907fc980b868725387e987a32f70f3191fd45c02056a9642d75f1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9822c952afcc336cc6aed2812422bd2140", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e986fd9088e17f6c80d3c4149baa53ea3b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}