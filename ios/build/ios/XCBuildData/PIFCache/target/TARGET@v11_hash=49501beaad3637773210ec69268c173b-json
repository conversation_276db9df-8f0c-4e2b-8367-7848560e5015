{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986c45782e55d4d2d3e2467b449ddd530a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824347c6af1c6483f68b7a8dfaba47059", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980958e5c147341f4eaeea8176d03f91e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98df6798f2f3726bd2dedd0471a741b461", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980958e5c147341f4eaeea8176d03f91e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c9bbee9faf050d52d6b8f7b8d9ec25db", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dc74c19201593a0ba7bebbeabd6d4874", "guid": "bfdfe7dc352907fc980b868725387e98902a579da93185bc1de6dac10756c9b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed010eefb1fa95c71241bb0de49d287f", "guid": "bfdfe7dc352907fc980b868725387e987dfefbd3d3ecc493cbeb7a00df46f1eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1410d5f699305cc2b77cad494b24eca", "guid": "bfdfe7dc352907fc980b868725387e98c76b1709daf897931ab0c99f856ff194", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815f8a328eb67b11d2268df055b7624b4", "guid": "bfdfe7dc352907fc980b868725387e98dfedb5d42fdad112653c17303806e5bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f539b3867b5249f264c89113a56c934", "guid": "bfdfe7dc352907fc980b868725387e98d49b72de62d2a15454ad63decacd00d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d1cf18fc0660eb7bddca156a227d64b", "guid": "bfdfe7dc352907fc980b868725387e98fd4a9872c73838a0144c9f45bc5d4a28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7fcac7d16cef38b6a5f827c7bb25da0", "guid": "bfdfe7dc352907fc980b868725387e98a4811928f3e46a5d7797d4c55a251dc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb3036c2372b022eb3f903d50af841f2", "guid": "bfdfe7dc352907fc980b868725387e982712088970bb4e430cd93fbad082503d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ae23de1bb899fcfb597fc3f42a71f7", "guid": "bfdfe7dc352907fc980b868725387e981cac57f06769ac534f250bc36b3d8776", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fd2aac583aff34b6a222cdd073000ea0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9859db2a2d279485d2eab7c354bb4eac2a", "guid": "bfdfe7dc352907fc980b868725387e983531b8d25aaa57b4abe8158c038479f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98876fdc69dcd58a2cee8b04ecfda21ff6", "guid": "bfdfe7dc352907fc980b868725387e98628a5367c3626e54a9717fd1bedaf31b"}], "guid": "bfdfe7dc352907fc980b868725387e98b53c02db4772d034d0e9a52fb22fa502", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e9870ea8d979e75aaf52415be4501fca361"}], "guid": "bfdfe7dc352907fc980b868725387e984a7dd80b53f82c925900c074b578a369", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9865570eda50e1f974b0a50ef069b3b8ba", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98ac9bb45ca5626d35cf838706769c3444", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}