{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da3bd4e7f3f0a5cdf59222e3835a4b83", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd0160ee1496a8837297ba0498c609cf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832fbe30ff5f39ce94f9f388b85d86465", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffe054710d952186caeeb4aabc6546c5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832fbe30ff5f39ce94f9f388b85d86465", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989084e133da975f93984c9b34ed0657e7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981370d5fbfc6852c0b956ab814912446a", "guid": "bfdfe7dc352907fc980b868725387e98f857449bb989ce062870636af0132948", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b031f4b92772eb67f3b5c853ccaf916c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e86adca7c4501d1b2284c4138e0e02f", "guid": "bfdfe7dc352907fc980b868725387e985af6fdb4f9d1eaaa1bb3b39a66a2d441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bfd6d3db564fbb03aa018dcb406fcd4", "guid": "bfdfe7dc352907fc980b868725387e98cf36afebdf23b504cdb45c7f1ff0f432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af0f6c2badffc5a8c3763ea23fd897b", "guid": "bfdfe7dc352907fc980b868725387e98c32be78b774811176af316c22dd84149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98838ef7f89b0dd292c1781ce7d08529f6", "guid": "bfdfe7dc352907fc980b868725387e9830d761f17b138508cc6e90ec0f61f16a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dbed1e75fd4b4b4bd140f6bc4d63619", "guid": "bfdfe7dc352907fc980b868725387e989270a45bb4812d3f77cd30f8f9c3cf6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b403d406fd3caf11b0d6ddfe6e65a02", "guid": "bfdfe7dc352907fc980b868725387e98479ac1de9eef6fd5b8790325900ee574"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98039d4db29d8bc248d6d8ea4703373c4a", "guid": "bfdfe7dc352907fc980b868725387e985d9255b768b40ba2b89568b9718c8aa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f373d94ad09c42d5f5de04b4717eede", "guid": "bfdfe7dc352907fc980b868725387e9818fbbd8d6c2ba109af9d82c1d1182505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb674ef3c0ed60f07c3037ca7cb3fa5", "guid": "bfdfe7dc352907fc980b868725387e98d12fde1085dea92a6de4678dd5070c19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c4d15434ca47401559b3405bbd62d4", "guid": "bfdfe7dc352907fc980b868725387e98cf27d864275beb1843a9bd158327dbb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d356f3dd4ca08e4e97fef87867eb03", "guid": "bfdfe7dc352907fc980b868725387e9884d9c76a22bf34d4997d72ab2c98211c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef05bf32af4ef8868d59a7f41b70e03d", "guid": "bfdfe7dc352907fc980b868725387e9820f8536c8160d25cbe031e316804be9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989929dd914f079eecf2ac64d76144ff21", "guid": "bfdfe7dc352907fc980b868725387e981aaac398453881ee3093a383682e58c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad31675a6233397a448e890a4818504", "guid": "bfdfe7dc352907fc980b868725387e98e99b2e57cffde4e9346709ece695279c"}], "guid": "bfdfe7dc352907fc980b868725387e983eb03bc823a0ae810dfba93325211b34", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98231d060dcc9dbce7f49beb9fbae16ce5"}], "guid": "bfdfe7dc352907fc980b868725387e987264d231f0c67cfb49f68eac3ed7d00f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98627014d971c23f4f0039b8d63c8a11e8", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e988f826a535579bf6961ecda218edb0f59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}