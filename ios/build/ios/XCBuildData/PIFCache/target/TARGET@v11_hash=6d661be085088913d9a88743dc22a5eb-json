{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980d435cd06864532d3405e40daa83a808", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98be27fb8898e19bc4f5e38a465a03bbb9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab02cfae71c721717c69b61398929594", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ddf9f04ffeaff5b492dfebf90eac1280", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab02cfae71c721717c69b61398929594", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c66f4e194c0ff0b82def70d9c0cc04ee", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9840b37fccc648f80348a678ce77206043", "guid": "bfdfe7dc352907fc980b868725387e98a8437165d610afe2236048742642b77f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985b7843af7508dbba28da43291f06ca00", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9854f53f989d1b614ba0c72a1fcf6e01bf", "guid": "bfdfe7dc352907fc980b868725387e981a4998db9094e7d896df1d4e267d3192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2f08c1c5dae9ba9d808a7587e42225", "guid": "bfdfe7dc352907fc980b868725387e98838911a9bf0161db8b37099e0c8ee6d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073bf2c45ba78612395c0bd19777ff9a", "guid": "bfdfe7dc352907fc980b868725387e98a2351967fe073ebdd5da77972708ec82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f137d378eeeb91ceb8a15abdb6254a6", "guid": "bfdfe7dc352907fc980b868725387e98860b9294e4c21ccf5a4b9355528e6db6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98262cc3dd93d9b0baad13d3f4b02704dc", "guid": "bfdfe7dc352907fc980b868725387e98be2be67e844842739181fd5cf757851e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af9c14e8de1bbd3f9b69adb82afc687", "guid": "bfdfe7dc352907fc980b868725387e9873e7fbf2b6c34d45262c14b9a10e03e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836afdb12ac99f78637f0c6415f806131", "guid": "bfdfe7dc352907fc980b868725387e987755492b140a39de5d52ab4d7f3d5018"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855794b21195a331691fc32f9c4abcc3b", "guid": "bfdfe7dc352907fc980b868725387e98b6f54dd286ecf14608dfd2ca48b318d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98285a4623df2f1d1f7006ad28e6d6f5f3", "guid": "bfdfe7dc352907fc980b868725387e985d96228ee8e0b41be49dc23dd404ef6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a4fc16926a431dda22202debfadc52", "guid": "bfdfe7dc352907fc980b868725387e9845520973d7dd538dc1d87420e346ea2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44436deb5048b94993ccb415c191881", "guid": "bfdfe7dc352907fc980b868725387e98777e999e1c6c9ddc152b28ee56d32af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d2caf7d72b185723f869599a4dbaeae", "guid": "bfdfe7dc352907fc980b868725387e9806aa50f759b03ce12db8baa2e8ef32ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e1d4303a147f3b92964ccd3f1cc7856", "guid": "bfdfe7dc352907fc980b868725387e98295ba9f610e536d26941266ae0288918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877bf86b38bd9f8dd8d10e9eceee7260f", "guid": "bfdfe7dc352907fc980b868725387e989fd568bbb879fffac25e53d3d5c40b99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a25472b92d67605aa1315610c6de8581", "guid": "bfdfe7dc352907fc980b868725387e98055a92c944679bed014b7011f91e3e3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12635bb753a110c0bcf592a3b21a313", "guid": "bfdfe7dc352907fc980b868725387e98d9bfa656ead8f05eba84a0c94f6409cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe831d48ef02582327c831372b62579f", "guid": "bfdfe7dc352907fc980b868725387e98957191c32726c84a2c49b52d20fd5f50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a174e9a8240fc18383f256539bb700", "guid": "bfdfe7dc352907fc980b868725387e9835e54a2ddec3da4f989a948844f09829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2232949aaf8845f677131f27e96438c", "guid": "bfdfe7dc352907fc980b868725387e989304abe84c845eb2b5687856ab0a8ca8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b04d6e4c78e6c2de50785829e983ff4a", "guid": "bfdfe7dc352907fc980b868725387e98c81fca990e53ac2f18d4acf85652a0d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d580f0eb9720c37fb94848ace9758a7b", "guid": "bfdfe7dc352907fc980b868725387e98ab8d9d13225dba8cbef262514f75cad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c515f10166f818f01423194544f16e3b", "guid": "bfdfe7dc352907fc980b868725387e9899ff34c8abb135f02ea136e1a562d3b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6fa88b06bf0cdb5a38744744dc254e1", "guid": "bfdfe7dc352907fc980b868725387e981ead86b242d86bce590ec1f2e79ba805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c11a68012bbf75037aba643c33e9765a", "guid": "bfdfe7dc352907fc980b868725387e98b287d626a2e6d5379a71889a21fb36bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987217b792d98b9715f9382db904b12927", "guid": "bfdfe7dc352907fc980b868725387e9855edd780a6df09b3a85f4b1a0fa71cfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98063b0a0faad7bb87d8a4376dc7751723", "guid": "bfdfe7dc352907fc980b868725387e984991fec287c6297409ef8d5d808f493f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e34c7c07c7ce44776f08ee450e052552", "guid": "bfdfe7dc352907fc980b868725387e984f431fda42f5138b36f51dd031083abb"}], "guid": "bfdfe7dc352907fc980b868725387e9841fac3edb70cac1fe722912d03ab0bcb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9895e83a650be86b53936346ad8fd2b7c5", "guid": "bfdfe7dc352907fc980b868725387e98ef982fdd511a37dfe559c059731a5d77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0316b1ab48eb16cc1080a47c3f281f4", "guid": "bfdfe7dc352907fc980b868725387e9839268a8bb851da85776b02e4decc97c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98111ce5a8b3dbd7bcd9cf6c0725d3aa96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ddaade9056ee0fc09f17fc9a6103e1", "guid": "bfdfe7dc352907fc980b868725387e985a9e2d5f3a7064e318d2d7ceac3bb4f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5fae1b72688673487ee184fe608cef", "guid": "bfdfe7dc352907fc980b868725387e989691cd4bab4947e51714bc7d79cdcbd7"}], "guid": "bfdfe7dc352907fc980b868725387e9879002095158eb2dfbd5bfa75fee6d11a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9836a7177cd39fb1ef0743e9fe0db05087", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e9882a1e5b4b5f4e3dd9c06cbb1c63646c9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}