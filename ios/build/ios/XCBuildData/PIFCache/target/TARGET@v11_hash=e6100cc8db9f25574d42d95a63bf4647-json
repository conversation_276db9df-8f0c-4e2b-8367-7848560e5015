{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bfac2243645c7376be942510c8b2ab3c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832f57634461bfc07541306e0e0909c0a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9812043025a3497c696ed3c207d184e793", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b77ea053d833db80a19dea3c0cec528e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9812043025a3497c696ed3c207d184e793", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fcf27a1859dbb654e6d1632ecd3da9c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e8809ed40294e85bbc31b1aca081b4f3", "guid": "bfdfe7dc352907fc980b868725387e98ba5a719f03637779d0e4114c173859a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6a55ba37f33b3d7d0ab27abcac1da1", "guid": "bfdfe7dc352907fc980b868725387e98e2307c7e3b6ededb064e5ddd2a68ec15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6df11d829c77d0367bc60fc465781d5", "guid": "bfdfe7dc352907fc980b868725387e982d562db17f8b5b4b452d2545362214e8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870cafe81fba65f59a8f31ac8c11340ad", "guid": "bfdfe7dc352907fc980b868725387e98d04ba8a39c5cad90f8f9d0692576eaa8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bfdaf6fc364d1521c8d0d9f92115216", "guid": "bfdfe7dc352907fc980b868725387e98c33f235531156ee0adb6b8d65a5c7830", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6aacb36b7660d4f5ec8f91388cd9d95", "guid": "bfdfe7dc352907fc980b868725387e985a895af414c847d231b5f4da7f1e0eb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee872cd8c0dd6f01a88c55bc94455c6a", "guid": "bfdfe7dc352907fc980b868725387e98a1f1dfde0a897a7a48297b39703ffe87", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802602bc8470ee07c0e83947e8002db1", "guid": "bfdfe7dc352907fc980b868725387e98cf56166b008f01c301c175ad0b214b71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872b14c7f8b3f70a0e3479fe0072d50dc", "guid": "bfdfe7dc352907fc980b868725387e98d42fe72f176a80ac9bc8f49ca458e7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c83a0ac34ba54b616e1b6b5a5dd2d3d3", "guid": "bfdfe7dc352907fc980b868725387e983f53c7c613094709f31b06d10211e623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a270d605dee9aab63ba0586618299a8e", "guid": "bfdfe7dc352907fc980b868725387e982529dcf37e031935f7df29cb7a0c72be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfe1557665ccb1b665f40ed51394c531", "guid": "bfdfe7dc352907fc980b868725387e98b957187b0e56ff1342b0658816919310", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813cdcec636ef9449e5c894a6bd601a19", "guid": "bfdfe7dc352907fc980b868725387e98d1e1455bdecb88b6dd994789b1a8d005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890454d2ebb8de9b199adbf9bada8ef36", "guid": "bfdfe7dc352907fc980b868725387e984dc9a9173228d460dce8bd05febcaa21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bb868dc5f900097aea2bd023f8b36f7", "guid": "bfdfe7dc352907fc980b868725387e9866e18cdc65ffb51bafc9666d133d6961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7bef84721d8f34b1d9524862a5a10f0", "guid": "bfdfe7dc352907fc980b868725387e9853fba8ec8f30e77eea0ee42d162d7b37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842975d44146537703307f5716acacdc4", "guid": "bfdfe7dc352907fc980b868725387e9850835d8912cad2b1f1810e1d3a3f127e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec106d9949c193cecc248520fe9e5cf8", "guid": "bfdfe7dc352907fc980b868725387e98625e8428f767980263004f084f2f06fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98266879dda5568a96a550d79fe23f6907", "guid": "bfdfe7dc352907fc980b868725387e98c30c5ac2b99a509847068097d1b90b04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d119603097863bea61e995dd48261537", "guid": "bfdfe7dc352907fc980b868725387e98d90e924c70c687d95153625ccf22e1b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9836a2081adbc8385e3ca55444cbc4c87d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984335b8213f0957287667d7f07d2c807b", "guid": "bfdfe7dc352907fc980b868725387e986b7cfccea424129119a7e0b7223d203f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98421a1acb9aba955261b6e63355c8d1a8", "guid": "bfdfe7dc352907fc980b868725387e987e2fd0030cc6bcc3a49952734b8e1436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eb8cc1ea5a277674ec5a2701910d409", "guid": "bfdfe7dc352907fc980b868725387e989addc411376632e0842314e1f3dbd3eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3f6e117e4d26e328820e04c318e125d", "guid": "bfdfe7dc352907fc980b868725387e98f2b75083ed4223737365239d09129b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c81131b3455b9a642951884a181235b", "guid": "bfdfe7dc352907fc980b868725387e98f6a65e94218fb5f98f3b3395aafcb285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1d4ca8cd5f9ec9a0a3946cbcab90e1", "guid": "bfdfe7dc352907fc980b868725387e98719f6caf81038a4fb6b36c401e41927f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a630d4451bc8c9a470996b5ebb27e03b", "guid": "bfdfe7dc352907fc980b868725387e9897454b0f741f6ecb896065015ddc7084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9893b4a40d6ae5725074445f5d6ac2e", "guid": "bfdfe7dc352907fc980b868725387e980555b502c9c0197b86706e000f128fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbfdbac6419e72912740ad1d9aa1846a", "guid": "bfdfe7dc352907fc980b868725387e9803c38b008504781c8e28657eb9ba9929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98915b6702b41d5af7f6f0fbd2fb4e61cb", "guid": "bfdfe7dc352907fc980b868725387e985a2ed91aac5da7e9139f8ae19e8ee587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6d78251558c717019bf9095538e4891", "guid": "bfdfe7dc352907fc980b868725387e988ad01aaa6d9324c69a1230aa30684633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca03c9b0376fcab42054a8559f0fef15", "guid": "bfdfe7dc352907fc980b868725387e9871bc7f2e968b7164541a22f887513a7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041b5ebce3c628bb51cb379b2cacbdd9", "guid": "bfdfe7dc352907fc980b868725387e98811905a9c4ba0e19039cca05ddec61d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821b7600113eb997b8cc04fb1a8b048fd", "guid": "bfdfe7dc352907fc980b868725387e9833f6e0f517dacc5b6398ab313854ccb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e977cf92746ba59f6eee8a631826f17a", "guid": "bfdfe7dc352907fc980b868725387e980bb1e730b18176ad52f058cae5417ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799a2d923e532361bf2c0219db9f078c", "guid": "bfdfe7dc352907fc980b868725387e981af43685ae73254d59946e622f376749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9528bb0ef022d75256a45e7ae2a97cb", "guid": "bfdfe7dc352907fc980b868725387e98641fcdc650b720a8dfa8fb181fa118f6"}], "guid": "bfdfe7dc352907fc980b868725387e98b1dde1f6d525a703b7caf328c78bea57", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e9844ec617da2f257b0b49d378a54c7b385"}], "guid": "bfdfe7dc352907fc980b868725387e987ca18eb2083c48f78e919a58dc322457", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c95c6b31cc49dcf5ba698c2dfa2917e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986716a62ee19f61fc991dd3f4aa3e1163", "name": "Mantle.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}