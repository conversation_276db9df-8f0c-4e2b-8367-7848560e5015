{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eec8288cd255204067155135e8ae6fea", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980bb535453386250e056d208a658313ed", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98626e338ba2a4354cd1218627dc676663", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982cee6643c7780a11f063e7f6f1494614", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98626e338ba2a4354cd1218627dc676663", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c9b004fab664b9ac0fb64a3ed33ec92", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98504a523f4519d845336882c3f9c81380", "guid": "bfdfe7dc352907fc980b868725387e989330be8fc80bb1cdcc039da3123fea34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bef26f969b524ad277c0b88e4efc21a", "guid": "bfdfe7dc352907fc980b868725387e98dc56178472ae1a22d2ae5cc3cce908d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987407a8b0d37c1bb91d9c712ca1da6910", "guid": "bfdfe7dc352907fc980b868725387e9881a73c5d181f7a26deb61f10bb8527e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fad799e4316e3560b519b242a2443739", "guid": "bfdfe7dc352907fc980b868725387e980cee99b35b51439b89e6a1b859f74321", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983175c1bbdf967908bace91ae283f94c3", "guid": "bfdfe7dc352907fc980b868725387e9848610ec30177a338111d5a9953c2039a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983213a433de284cbaa796e2f747fb140a", "guid": "bfdfe7dc352907fc980b868725387e981f24af976fef5be49802360e4c2099ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1e00dff6fde0f6d8ab3bfc33af22db", "guid": "bfdfe7dc352907fc980b868725387e983cce25c0eeaa861dd4ed28d271fa2fd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbaff5a55dfe99a25a0668530b39e634", "guid": "bfdfe7dc352907fc980b868725387e9819d244b6eccd50bcb3c5b6d52ed5fb6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a64e3938fe7d038467c1da440a7eb5", "guid": "bfdfe7dc352907fc980b868725387e985537030a9530691373772ab248c9a988", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379766a79d11476b91181cbbdc576dd6", "guid": "bfdfe7dc352907fc980b868725387e98afc304cd6bec9fb1aa1cbb71d464f819", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d42602c6e6081b8ed79491839fc3c4", "guid": "bfdfe7dc352907fc980b868725387e981914e7adad40f07f9795e65a6fc93ac1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98936a6fff0f8ca7275d588185cadd109b", "guid": "bfdfe7dc352907fc980b868725387e98c1595a3c1f3d3d5317ceff0cb5d53f50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822ee20a4127252b960fbe8ab55bf6b37", "guid": "bfdfe7dc352907fc980b868725387e987b7c21214e743daeecd1d8c9dc8a20eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed0c71d3eaf529526fbac8fa5d84a647", "guid": "bfdfe7dc352907fc980b868725387e98ece7c2ca1c2a9af0e85cf08f0df5889b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f2dea4f35a7b9ef2df8c9c34ac7817a", "guid": "bfdfe7dc352907fc980b868725387e98019d0d335d4dd776564629a12ccc3216", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b7086fac764f9820bbb5b941ebc084", "guid": "bfdfe7dc352907fc980b868725387e98902f005f952c9d192b34522980d3ed67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982da3089bd671a288e427b9b19a03ae67", "guid": "bfdfe7dc352907fc980b868725387e9830aaf226db90bbe2e435ba54dfdf7e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f49e2bb66b147af7a98ff23a8e2a87fc", "guid": "bfdfe7dc352907fc980b868725387e98e9d48c4922caec6b6a1997fb09097fa4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e119eda1b9e6b29123bcbff82b2f0baa", "guid": "bfdfe7dc352907fc980b868725387e98feef44ae7783f3cd8cdfd6813f46968d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876a2b2afdae5b50571e732caea85e57a", "guid": "bfdfe7dc352907fc980b868725387e98745a2b7750f37bf49742501d160df563", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890cc7e598116703fdd2d176ef4a519a8", "guid": "bfdfe7dc352907fc980b868725387e9891473aaca16022b6517fb621a83341f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986657b6335b7a4a776763493b3ec70948", "guid": "bfdfe7dc352907fc980b868725387e98f1104518f3ea2d8ec0b28e63153496c5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984b8150bb4c4fc430aa3efa7b5145f1dc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980bc6c2ae3a0560ae166f22d392a5ed82", "guid": "bfdfe7dc352907fc980b868725387e98036f2db90afbed1d918b950aa1be417e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106b4564ac6d1bbfba3279fe9098873a", "guid": "bfdfe7dc352907fc980b868725387e98e3a1c6537d72f9b62e8c0470ceef6c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fe391dbbf602154e7ee446805115ff", "guid": "bfdfe7dc352907fc980b868725387e984dfd155ff291a08b0b190015c21df62d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abfeafb988a56d95ffe6762dcc2b64fa", "guid": "bfdfe7dc352907fc980b868725387e985c870a50b79e5304636ac8dd80e558a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98599815188d629216d768d6be962f7f9d", "guid": "bfdfe7dc352907fc980b868725387e9807299763bf90a4028886d500c001cfd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc61748342e1d9da7797e604c8d0eb7", "guid": "bfdfe7dc352907fc980b868725387e98233aa4b278c234c76ff274e08af4a35a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e96cda6d292184c12951d752746f7a7f", "guid": "bfdfe7dc352907fc980b868725387e981a81141ae9b2030af9417239e56f72bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe7106620703733d435ff9ff2be3c8b5", "guid": "bfdfe7dc352907fc980b868725387e98bfbd4916e9d31a6d4514189cb4a7988a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e501f4dded1ba60c623f6029d0f5f89b", "guid": "bfdfe7dc352907fc980b868725387e987a331f1a71a9e9dfa80f0a7f2c382dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39ca0b1acaecd836b92803045a9d462", "guid": "bfdfe7dc352907fc980b868725387e988208f366c694c9986cd1698295339f50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800d48a550caddfdb82ca00f7b9821625", "guid": "bfdfe7dc352907fc980b868725387e984125b544c389305b0f89a96448d1c508"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987944b2f70e9f5e5f003e81153f069379", "guid": "bfdfe7dc352907fc980b868725387e986f5c33bb5ca05973c9e811407b08e4cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc72938e68c1bc2def6a340c96060a3", "guid": "bfdfe7dc352907fc980b868725387e98d3704f117312a9d25dc0b31788d90f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb4c4f32b59f34d2d64fa44cc05b84e", "guid": "bfdfe7dc352907fc980b868725387e982c54d981840e9d2bedcba79ec1fbe8dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980501630657a43668f5a8b5d7f904060f", "guid": "bfdfe7dc352907fc980b868725387e982a9df16ed1375a9f2b17d581be5da7ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1cb8e34ab5b282777878957ced53df2", "guid": "bfdfe7dc352907fc980b868725387e984eeef9546e760cc91e4af7a5e661d20c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cf85e6b294c10d9957a638f83bda0d8", "guid": "bfdfe7dc352907fc980b868725387e98e5ef85c8c9882f6fcf44c04486713185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016a2f9b5ebf0d35587c958882ce92ff", "guid": "bfdfe7dc352907fc980b868725387e98bd6977729f273915e1a2f51b507d8106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6c72b2f3d95a6694419ed6546957bde", "guid": "bfdfe7dc352907fc980b868725387e98354b9df34453a1aa8989052d4b548ba3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e1f7f270649dd44ad19b8141e8eb8e", "guid": "bfdfe7dc352907fc980b868725387e9864ecfc7ec3c3782b68a5e7dc53a8186f"}], "guid": "bfdfe7dc352907fc980b868725387e98ca318c5e28879d8a8d1e7d8cfc7894f6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e9852651479c7234183ab6861cdc0745ae2"}], "guid": "bfdfe7dc352907fc980b868725387e9853abedd59935b45b41795b3daa7d37a5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988526768e43e53d5cee9cdaab325de1b2", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98c6114b4447b0d32f29083a9a884cb172", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}