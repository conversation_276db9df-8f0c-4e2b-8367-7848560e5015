{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984510e1480acad470f1aacaf9d798b85d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e1023124d5386f2166df1f2d3e982a3a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872b8e31346b909c5c4b709199ce76cfb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a7b00a8332bc5a8c7e3bad0d6cc3978", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872b8e31346b909c5c4b709199ce76cfb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e149248441ab5ee86a3a0439d062deb3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9817b98a093dd20dd98b376f94b1087450", "guid": "bfdfe7dc352907fc980b868725387e98f4f13489f96a485d04e9aabfcb186498", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49cccca21fa15b496a5a8476edb3a10", "guid": "bfdfe7dc352907fc980b868725387e982179407551dd9daaf5ba48d96e123f51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b65524ae3760b8d27f1ff3dc661edef", "guid": "bfdfe7dc352907fc980b868725387e986b7305628a6f5d886799381633809dc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856aed3be502eeaaeab274dd2c539e4de", "guid": "bfdfe7dc352907fc980b868725387e9895bd754120f9951083821e151958c30d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5836967ea838e0956b92b794f203c3", "guid": "bfdfe7dc352907fc980b868725387e98e2ce690b0e42b8e0afbc2e156c649ff6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0892723436d1a8855a1921ad1919c44", "guid": "bfdfe7dc352907fc980b868725387e98e4fa758f4bc9fbe9d6b06a3064b3ce88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5731865608c2d5978af3b65e48c94dc", "guid": "bfdfe7dc352907fc980b868725387e988b5ce1fbc441896abad06f02b2d5f6d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca182e1fac06b19823ad29a151c4b45", "guid": "bfdfe7dc352907fc980b868725387e983a0fbfa36b1f252bbbbc1085985a088b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b981afab67fb579a0955b3e5ce66894e", "guid": "bfdfe7dc352907fc980b868725387e98370b65e8e6b3425e3f393cf8353176f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd31519d3e9ede60e972cd0650738116", "guid": "bfdfe7dc352907fc980b868725387e989a0456fcbc158c72d37979c22c3121e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98841596a43d74341bdc5dbf5c6e09bbc2", "guid": "bfdfe7dc352907fc980b868725387e9833bebb2ac37df511fc44b0fe09440910", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980264894e72df0b91d7adfd55a76efdc0", "guid": "bfdfe7dc352907fc980b868725387e98e546eb2a185614a2f9ee3499ba20a639", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851900a68dcb00f1d049cda6454c54b6a", "guid": "bfdfe7dc352907fc980b868725387e98b96405f14371b1c18dcb13d0e75af393", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ab27d238780cbaa114603480d6162aa", "guid": "bfdfe7dc352907fc980b868725387e9898d51b5fd99bdc8277efd57d6e16c945", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec540d33c441c12fd73197a083c31334", "guid": "bfdfe7dc352907fc980b868725387e98ea655b6a52432341ca7a19505e20576c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd1f800f6747d4594169ca66a56a4955", "guid": "bfdfe7dc352907fc980b868725387e985a6f288a3c1ceb720eac0475b6f88ca6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986058721319f54f91417fddbfbf55b136", "guid": "bfdfe7dc352907fc980b868725387e98ca87105f43de0afcecb1d020f146bceb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd160e08acaa4e4220b67ed4899cb220", "guid": "bfdfe7dc352907fc980b868725387e98b3faa2506b90b1fe5bc5eec64c9e539d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b775eef39dd54ae854dd309824700786", "guid": "bfdfe7dc352907fc980b868725387e989d5360d196151898743cdf823f8a35c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fd1b766da1b5612f98012b4106936ae", "guid": "bfdfe7dc352907fc980b868725387e98f031e5e1fa852ca45f5870706a5850f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98609044ed80f840a0818ad06886227515", "guid": "bfdfe7dc352907fc980b868725387e98b04f0e40c56441b3b712444c7d46a2f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98120dee978cf932457b15d219bef6374e", "guid": "bfdfe7dc352907fc980b868725387e98daad38a80df8f3dd11c87c3b4bcf4008", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bee0a68a44ae859f68ce08357017bef", "guid": "bfdfe7dc352907fc980b868725387e98c6ac142d8ae4baabbc317e3bab1aa5e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac08728db9832cbdecdf20d632686441", "guid": "bfdfe7dc352907fc980b868725387e98749a6c61f107c5513598b9ce690c5e46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408d14897333ee9c2509134619b78443", "guid": "bfdfe7dc352907fc980b868725387e98b9b8eaf514a408db97bb5820bbfa05e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998e14ab799b3a02aff7bad7950ffdde", "guid": "bfdfe7dc352907fc980b868725387e98778e180db742528ca54906325b780bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a4e35a4fdfd7c6ea2df447d5221af7", "guid": "bfdfe7dc352907fc980b868725387e987b0e78882d3d9ab2364d348461f55064", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2b3be022450c9cd4a324029877631cd", "guid": "bfdfe7dc352907fc980b868725387e9834331c3f95ab07e592ac1c120b020a03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b910374acd2db2dbc92048898ed39041", "guid": "bfdfe7dc352907fc980b868725387e986258a984916a667d3d21d48002552450", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d0b41dfb9a49d722f6f070574a07e00", "guid": "bfdfe7dc352907fc980b868725387e98bcac3a7bc06f3a7ad23aee7e05d795f8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fe433c649d581bb3cbf58c8b43395094", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818dcb3af3784f0c9c2a7fd43b2fbce35", "guid": "bfdfe7dc352907fc980b868725387e9853edff738ff2b9e200589b5869400eca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fbf193d2e07ef5a37afcc9b690517b4", "guid": "bfdfe7dc352907fc980b868725387e984c64348f85649be07a1f8496c660e11f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813caedc2f98bd0f7eef9a0b29e3a5b38", "guid": "bfdfe7dc352907fc980b868725387e983cc1b08a3b08985277d1f7c8789b5733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c2bf5b077b607946a30d62556732e8", "guid": "bfdfe7dc352907fc980b868725387e9861d2d067e4b835d3629b03d0d7f58226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980adee084801615e0d58c4cbeab3553bb", "guid": "bfdfe7dc352907fc980b868725387e98859e6337a7e8c2d0b3447463d6382099"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984043b9b98ac2b152857e6c7cad5b2c", "guid": "bfdfe7dc352907fc980b868725387e98f57d8103003a8dace4d4505244f1cc9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8dd0c8a8aa6bfa149237f76b0990585", "guid": "bfdfe7dc352907fc980b868725387e980b84ccb7b9913e69daecd7f6e9063630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afab8a36ac6b1feae2d1d6ebc3b3ff01", "guid": "bfdfe7dc352907fc980b868725387e98aaee363a2c7b9df8686e59a7a949fdd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a357b7b536b875e430edeff0ea8b81", "guid": "bfdfe7dc352907fc980b868725387e989d57c1820d7e77500982312bd2e331db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed802529dc706786e54cff22837d4253", "guid": "bfdfe7dc352907fc980b868725387e982f1099d6c4980743f15a58a6e340135e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98279cf2890605142af2756178d33d5d3f", "guid": "bfdfe7dc352907fc980b868725387e988bfc471d668f3718f4814eadfeb23c20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98224c72b97bb58bf7983b662ab0686e8b", "guid": "bfdfe7dc352907fc980b868725387e98d1bbabc39127d2c43755e8cb650ba5af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabe8205737b5f848d2631f14dd3af95", "guid": "bfdfe7dc352907fc980b868725387e98e1617b2379cdced59c03c26074a0479e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98721d8e44344b396a7b182cf2ad2dfe56", "guid": "bfdfe7dc352907fc980b868725387e98625e27928d50c222af4c5e5cfc714980"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d322a0c772b0ec5fc0e37147c913cb4", "guid": "bfdfe7dc352907fc980b868725387e9891b1cfbd4ad8ca2824586f947acd095e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a215c5c41bd0b3fcd27624fd3c9cbc", "guid": "bfdfe7dc352907fc980b868725387e986ed08d248d61e4d0662f5208cb64cffa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807905c26e43fcad416e54d8a0c2e0904", "guid": "bfdfe7dc352907fc980b868725387e986d8b4a2b94f8bcfa13e3ca026c239001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b83bfb8f89543b3ca5dc562e25d5cb", "guid": "bfdfe7dc352907fc980b868725387e983410458697ace6a495e6316da1423f74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808fb0c400d354c9a5dfafa4cbeea2cca", "guid": "bfdfe7dc352907fc980b868725387e9856161182d00bf0df345e43372f5016b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f65634f2d8417d5085e8093daab8f9b3", "guid": "bfdfe7dc352907fc980b868725387e989a4052f61af4a5e424033d804d5920b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98384d782da33f1c57c5e07b1eb958b818", "guid": "bfdfe7dc352907fc980b868725387e985d00af03c75439d4661f45034b2fa0e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da2002f28eeff3a7a9d81eecef986ea", "guid": "bfdfe7dc352907fc980b868725387e9829b70bcbc261488ff2d063fe469b7441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edfe3b12d77f23eca028631a0a19f96f", "guid": "bfdfe7dc352907fc980b868725387e989aac7e0ecca723466684c2b021b3fc04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7a164fdaa7a56f988277565a2c535bc", "guid": "bfdfe7dc352907fc980b868725387e98fe5ff1626b6478d2f2ad1e6ae9f9183e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98417c80fec364ccfaafca1e8eba0a289f", "guid": "bfdfe7dc352907fc980b868725387e980358b9df7be430733461ba1b37150485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48bfc2f1cb9e64d33f096bb9390853f", "guid": "bfdfe7dc352907fc980b868725387e98c77682cb7031835e07559a6237b41165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7ed4cb068f9ece5f0ec2c3e8cc1d61", "guid": "bfdfe7dc352907fc980b868725387e98591e1616cdb83956d55acde0f2537449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7774a573c58c713f3cd9df630c533a4", "guid": "bfdfe7dc352907fc980b868725387e98186d5e32c2dbd639714f65a2a6f90cf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c35b90c50398544c7b879557cff17fd", "guid": "bfdfe7dc352907fc980b868725387e98d6589b87119ae0bf0ab61c192db8d016"}], "guid": "bfdfe7dc352907fc980b868725387e987103679c45be34718b434087b45592e6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98ea5cd65582b34e538df25d062e6682bf"}], "guid": "bfdfe7dc352907fc980b868725387e98b759de21efcbfea72ee35c7cf86b1aaf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9818ba9fa6de07bcb490d713cec7d278cf", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98558a9c69dd675dcd733f79e56b9066c8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}