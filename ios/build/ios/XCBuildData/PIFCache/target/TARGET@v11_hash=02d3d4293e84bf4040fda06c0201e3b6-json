{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd7ef14226d3e9a9b46084baef2a61a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982539e447020de8f0465d0db79bff4271", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4dfed6e17020adb3b9ffe60d4cac784", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a37f8ab1938ba2758d3e231fddb4f5bd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4dfed6e17020adb3b9ffe60d4cac784", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98df2b5b4bc0eb11f9ff3adfc0a08ddebf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9809e5b1d1dce2cb0d6c31f9f3414d7328", "guid": "bfdfe7dc352907fc980b868725387e988c87c7b0f121634f661593278a6c8d10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98156903f2b8e136fcd43caef101a00e58", "guid": "bfdfe7dc352907fc980b868725387e984ee92739fbf2a8804a6f4b04a3735e3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab11ad1963fc1b9a9037899c243772a8", "guid": "bfdfe7dc352907fc980b868725387e98098bfa1c36298464bf2b49be607d2a85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895459b8b1e166402ca9503433c09be4b", "guid": "bfdfe7dc352907fc980b868725387e98774aa0a258fd06b231cdef108a70afcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbc173b26162f39d05f2585957818c45", "guid": "bfdfe7dc352907fc980b868725387e9885e738addd61231ee1afde2424e1b1f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ece0d36ab9b6e50c6913db8f8f7ea483", "guid": "bfdfe7dc352907fc980b868725387e9878f2fe01f6f064047e69afa2b852ceea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865e7968281a83e3ea4d820822a15232", "guid": "bfdfe7dc352907fc980b868725387e9859a8b34eb76df3e6341d10d419f8630c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fac10a23e66da5bd6a51c03e779eeca", "guid": "bfdfe7dc352907fc980b868725387e989797e03bff03b1c0eac497d6bdc8cca0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823b9e2f958d541ded6c9c1addb6f07eb", "guid": "bfdfe7dc352907fc980b868725387e987c0ef655ce7b67c62e9f11552c8159b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815b48aa47921c023d4d1ea6a7a5dbc11", "guid": "bfdfe7dc352907fc980b868725387e98cd72389f7a6472c77ebc02cd39dfbabf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48bd1bf4d823aea61aa8ae9f6afea8b", "guid": "bfdfe7dc352907fc980b868725387e98f3556a261b4eafecf9eee86e2eeaf4d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c1eef03faa114fd0a5be7126914971c", "guid": "bfdfe7dc352907fc980b868725387e98fe22a56a9c402742ea3bea3b68ae3a16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819abe198706f1809e61defda609e4e89", "guid": "bfdfe7dc352907fc980b868725387e982144a10567748f75ff9d7eb47fb900c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc409fa00dad8cd86528af5f80d308a4", "guid": "bfdfe7dc352907fc980b868725387e98d6d54d44d4013b792330f939582b8d35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb0895db2fc6c81adf8df29d3c7b57b", "guid": "bfdfe7dc352907fc980b868725387e981f9ffeffe0678eb7dce603838617340e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ca75df83f3d89866f84853bf81b339", "guid": "bfdfe7dc352907fc980b868725387e98ad3a437eae0551039de337e6f05f3695", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981004674dc48a465229b0eff8cfde9e45", "guid": "bfdfe7dc352907fc980b868725387e98a3ea25f2ba1e8fb007cb7c9487cf3818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebddd324a6509700ff765a87ba888b47", "guid": "bfdfe7dc352907fc980b868725387e98720ea2fe1ea63cc55acda44ade24db9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4f57de7ea7ac414af6e9ee481ae69f7", "guid": "bfdfe7dc352907fc980b868725387e98bfbcbb3a1b5d6bde44b89df7c2823168"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987295eb57ffcf527c5db8220e9752d23c", "guid": "bfdfe7dc352907fc980b868725387e98ba5b3db29a374c01886f1276aca3eb96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc7fc614e0a2ff8b34aa67d0253b903", "guid": "bfdfe7dc352907fc980b868725387e98c56cd280b6f49129438cf0dcbee0f81a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a4315fc71ce1719edf2fc64b32018e6", "guid": "bfdfe7dc352907fc980b868725387e98c158472467cf4693991fdbf26adc5e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53d2ce0ab14a2c91c90aa23011c2578", "guid": "bfdfe7dc352907fc980b868725387e988acf87bb1ee9ff4e05c066042316e350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94290c642e80bfdd7f85c342928b01e", "guid": "bfdfe7dc352907fc980b868725387e981f57678d9b315d7cd895c3146378b9ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df5c2cc507526e8975f6a8952c8a66a7", "guid": "bfdfe7dc352907fc980b868725387e988ef8e74d42d4aa45cbc2adfb4eb8257d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988581f20b8d89b842c1b4a77b9380c04a", "guid": "bfdfe7dc352907fc980b868725387e983e870b8f39120912e28c6fbdd45a2fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ab07b7f7031cea175a95c460789fa9", "guid": "bfdfe7dc352907fc980b868725387e98d31d13588f7b92222919164637dcb66c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fcccf56efa5cb5375272b52b5cac484", "guid": "bfdfe7dc352907fc980b868725387e9892d446a7a9ef226a98998209bc8e6775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d21f080bc590e925cc4fd0c9fc67dab", "guid": "bfdfe7dc352907fc980b868725387e98f0c4d0b66369c1df2d70920d6ed839da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864e87df4747c2cf6f7faeb58ef36ab61", "guid": "bfdfe7dc352907fc980b868725387e985c6c3d773a6dedab23fe2327c7ef4c14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc1bbd812b05202c8f44b4e39eecffc3", "guid": "bfdfe7dc352907fc980b868725387e9801b6c96927a73c1cf6a026b706f34b53"}], "guid": "bfdfe7dc352907fc980b868725387e9866145b05fa7cbff3bbc5bd7cd6961f0e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dbd90d52564baf7e78f1ed62954565dc", "guid": "bfdfe7dc352907fc980b868725387e984cf9acf9bf2ad1c2d42e5cb170337cc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98319a629f1380ae8da8c5ecc5185c9635", "guid": "bfdfe7dc352907fc980b868725387e983f19a479927ac7598ef869997c028e9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98867a1e3dc7f8a2968d9f29987adf36ef", "guid": "bfdfe7dc352907fc980b868725387e9842a67106a307d04f90de3008938f701a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f6299e2ae27e516c3f597dbf70f191d", "guid": "bfdfe7dc352907fc980b868725387e98893c0b16fcce5856cb9dca2341c71735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988777689ad4da2e16495e57e60208202d", "guid": "bfdfe7dc352907fc980b868725387e982ce63e8f2dd5022fce675b0e7209947a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c425f313bccd71c165fd7b749ba0d0e", "guid": "bfdfe7dc352907fc980b868725387e985cb6f9162f4b9ffe83a9ced9d213b233"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e7bcfccec51ba6acd593a7cdb58260", "guid": "bfdfe7dc352907fc980b868725387e981051d0588df9b6f90e650e0b744b39a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980789c71c1a5c557019eb12c713b393d2", "guid": "bfdfe7dc352907fc980b868725387e986ab99714fc3c6cf824681992b0e46d68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea422cbf252da1a4951e6b72e7969a70", "guid": "bfdfe7dc352907fc980b868725387e98f8804339b17e258052dce35707ea4dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfb6cd329866981d7fed06b52be624b2", "guid": "bfdfe7dc352907fc980b868725387e980cc32fb7707ae5d27fc0c927ec18d1b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982979e96891609700a3848ef24c407153", "guid": "bfdfe7dc352907fc980b868725387e9828dd8cde5933a97c949520c19b636642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceaa7366c4febd020ac26008da424b30", "guid": "bfdfe7dc352907fc980b868725387e98ca7b984490160a9b74e75e06ee27051d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e68b1bba9aa8cc3eb8ebcacf47c3f8e6", "guid": "bfdfe7dc352907fc980b868725387e9827c4fb7ffbee98f7a40a3d75b565ad4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988943c9d14759fd114785bb320c4f1678", "guid": "bfdfe7dc352907fc980b868725387e9899a2f39de83971e2286311d67b2742f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176e62fede65787b5f83593d50ebfc93", "guid": "bfdfe7dc352907fc980b868725387e982e9afc343d7c082e207a040d086769a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cee9af6ede8df056403ff2fe328f892", "guid": "bfdfe7dc352907fc980b868725387e98514b7bfc7ff8dc4127651ea9e14aa9c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c178b7648eaedb7939b05f76e7761b99", "guid": "bfdfe7dc352907fc980b868725387e9831717f4a7e76ed7a144367c9197f124f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835a7f7d09883f49a4d2a372181293aa2", "guid": "bfdfe7dc352907fc980b868725387e9825916b7e4188f31a88ac1fed1176d686"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d9a88308b673f0f4a2cb1f0b2022ce", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e982a7e7c4b01cdc27c2c35b3db3b7ac36b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9187d35bfda3f41ff6bc7539a79d0", "guid": "bfdfe7dc352907fc980b868725387e984a378ca0ebc43482e77deea063e1735e"}], "guid": "bfdfe7dc352907fc980b868725387e98c9180517d945a9f4d6d2474854dcda5c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a08539c856b3798807aa034f64a50c41", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98da6c708295f601310a5d17b01dfe3255", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}