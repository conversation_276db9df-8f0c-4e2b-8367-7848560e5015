{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980eb509e748eaa3f6822a52ff46f3a248", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98099dd6626b3e76573f35d0f060c6e482", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897af29b2e9e22631e73a37cc29700c1e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e631e73f04bc3f7145415bb0a5edca23", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897af29b2e9e22631e73a37cc29700c1e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e3dec2dca88b712e5ee70d185cf56468", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9804a2807631fe27ee984479850c9b820b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98eb7360d125ace9038757c241108d34f3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980fd584bf4ac867601b5571bcfe07348e", "guid": "bfdfe7dc352907fc980b868725387e980569e02d43948410df2b7b722fecd030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98835546b1104b2181f233561ce1c77a7a", "guid": "bfdfe7dc352907fc980b868725387e982fd5bcb8803a8fe1f9e3b84a8842dec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a311788e6350e7765f4a0d566a9e92ca", "guid": "bfdfe7dc352907fc980b868725387e98de903d3cce06a998fd49d05676a7948a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51e1db81968105b90724888811f724f", "guid": "bfdfe7dc352907fc980b868725387e988736c33d519fa6eb0d6142cdf2ca6036"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983827e2fb1caf2143ef93e4ae9545dbc7", "guid": "bfdfe7dc352907fc980b868725387e980e2aa6677e67a9c1f8bc7c1881e177d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842bf0bfbe6d6cca0d9c1c918fb53d9a6", "guid": "bfdfe7dc352907fc980b868725387e98c6151b20813048df2e191a1ad2b00b3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab129a6abb81d8672a3ebdc5d06d3e3b", "guid": "bfdfe7dc352907fc980b868725387e98588d65d1e714d76fc0ad36b89bbacf09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ddc336240922fbc16a484c5ab73ede", "guid": "bfdfe7dc352907fc980b868725387e981e547cb06ac997ea4287a47ab79cdfde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818ff05731503bf88868a5cb11a761d18", "guid": "bfdfe7dc352907fc980b868725387e983913da7ecbff8fbb52a1f21e01d2d955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2b5b7f251e35eca1d71d5830860f12", "guid": "bfdfe7dc352907fc980b868725387e982481497fe450d2f5d23b3198a79c4256"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836856b9e127c92393142ecd533820b27", "guid": "bfdfe7dc352907fc980b868725387e9882268f35b4db0e236c88ad402c065f57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca3219990ecc53cc434bfc215c6a4cd", "guid": "bfdfe7dc352907fc980b868725387e98570ad47d73b2c6131fb2e7a516317bbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533d5c35b67d9209ea52a43398bbc4a1", "guid": "bfdfe7dc352907fc980b868725387e98cdcd823217c699720aa6e8526fe1cddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7cfadf021e1da5a0ba4e09f1ee0adf2", "guid": "bfdfe7dc352907fc980b868725387e984c3b8a8ca85d20361e0ebeb37878aba3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ca70858f2968a0eb6aedf238a37db1", "guid": "bfdfe7dc352907fc980b868725387e9878919a59da487677872a758febd58581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc5fec34fcc8a86437be5ace72a9e13", "guid": "bfdfe7dc352907fc980b868725387e987c4e25add69b0872fd70bab1841908ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f80a890f7c52dd03cf75536e8850d05", "guid": "bfdfe7dc352907fc980b868725387e9867e6456c14ada9930987c411e2e413af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53e1f094225776d8839e3a60aee9767", "guid": "bfdfe7dc352907fc980b868725387e98583a66cb1826575f4d5b4d09c4e1f705"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b20f7a32d914f43ec3155745d89c9578", "guid": "bfdfe7dc352907fc980b868725387e988f646111a3204ba08cae97e64f1142b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a2cf434bda3e6303bdb577317c8e414", "guid": "bfdfe7dc352907fc980b868725387e9861353c1db3171676052a3da7dca83714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985791e7984ab607833abeed5421e21437", "guid": "bfdfe7dc352907fc980b868725387e9812e1d30f296be924e551384db848f812"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ee26fb668f9c8ebdbe4247b64a863a", "guid": "bfdfe7dc352907fc980b868725387e98239e92190d1954bfb2efcf2fe919fb66"}], "guid": "bfdfe7dc352907fc980b868725387e98be13c26f0ad6723008c978e3e2c87876", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}