{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7f2a23379b268ef71e542d270f9c565", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827846342f28950f96c0e8e709582841c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9867f432d8267743aab02b83be71a83a17", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da6c3d8cfc8eae66b3a5e97c84946620", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9867f432d8267743aab02b83be71a83a17", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed801f2d8fa02d02b78c11fd69aeca35", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985f456051a1e80830d4150bbfd1c70c6b", "guid": "bfdfe7dc352907fc980b868725387e9840bbe250404e8b0b86bdf2345c43bde7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c980f4b541311bceb741cb577e8173b7", "guid": "bfdfe7dc352907fc980b868725387e987c1067cb90dce73aaa1eec68a5b75f88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fc1ac68b03a1e6aa57ff94d2f6040c", "guid": "bfdfe7dc352907fc980b868725387e98a5b3e72edace5c11dd6ab3ff34027fe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84def3870977355c0e5611b55590c1d", "guid": "bfdfe7dc352907fc980b868725387e98b4b7a84e06d629cc195df9b5a466c15c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1f7e16a729a05c9a86d1dcb39c0cb99", "guid": "bfdfe7dc352907fc980b868725387e9857b95d95e2a6e713a56827e531875274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98903a769557be56776f06404c6bc55ac9", "guid": "bfdfe7dc352907fc980b868725387e98a41dbaba5294ee908a58a6a424dd9551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bb33d2fabd06d83a3eb31a94c4993a2", "guid": "bfdfe7dc352907fc980b868725387e9809af6eed97adb0f9d369eb9dc5308025"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801bacaf3920213a4915be4bd5b89c6e6", "guid": "bfdfe7dc352907fc980b868725387e98507092e177bf8910a988246817c06e0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864afec203c32a0fedf3e2d1fdcd15bad", "guid": "bfdfe7dc352907fc980b868725387e98cb41b75cc8efb44e179f7e730cdd396b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e738b04051179321699a10e34c02a2b0", "guid": "bfdfe7dc352907fc980b868725387e9872781e997e5422926ff95fdbfdf3c741", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f313e889667aec198c1af5a77d15854", "guid": "bfdfe7dc352907fc980b868725387e98accd29f89a241ef88ca4a4f78d2655ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823641969e2ff0f36f539a9c21404bdfa", "guid": "bfdfe7dc352907fc980b868725387e98c82c8ef3336848981a4493107630b019"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982be849a834df6dad09e9d4ec80577ee4", "guid": "bfdfe7dc352907fc980b868725387e98ffe4ff3b0275346f36075fb3bc776283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827feed16d53ebc141ba3d98db8d4bef7", "guid": "bfdfe7dc352907fc980b868725387e988c072b892993aa0ccf117b971a2f246d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823e994993b5219d3f7481de20a109fd4", "guid": "bfdfe7dc352907fc980b868725387e98cec4dc5f0be856febf6847f6d37df051"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e59ddb4bd8cce8f59847e69ceb05b70", "guid": "bfdfe7dc352907fc980b868725387e98cba6b3208a5863aa0b2c30cbc578c94b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b973d0a8d4152db216ad8888a34f1940", "guid": "bfdfe7dc352907fc980b868725387e98c8ef0d31db389463942f172f3910984e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d98c2b1fb83a922b4630d7195ce65db", "guid": "bfdfe7dc352907fc980b868725387e9842e3817f1bfcf352fbd39200d454881b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa215be4f05648ead4ad1ed069ad4d3e", "guid": "bfdfe7dc352907fc980b868725387e9870da8f4d305327a9a3039ef517e122d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b494d6c55ba408d67fa040e6253ee8", "guid": "bfdfe7dc352907fc980b868725387e98924ec2e8eab108662d84c22daa941892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b43dff211b0b8cd47b68a799a8bd23", "guid": "bfdfe7dc352907fc980b868725387e98cf89e5bf28fc04ace5d62592a7ce3bb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986285a3537e4de731eb043ab09d0b3aa1", "guid": "bfdfe7dc352907fc980b868725387e980bfcb1106c8d688bbc01156861062671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd0eef7b188f98cc9046ff4fbb4e9ee0", "guid": "bfdfe7dc352907fc980b868725387e9848967aa7cd2c37e014bb30b290f985b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c26e9d89c31fd868fa989f5b54429b5", "guid": "bfdfe7dc352907fc980b868725387e98ed6b034afe6d66c725f28fc9066c2a16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa9ca07e92fdc3f0f7f0df43bdcafd8", "guid": "bfdfe7dc352907fc980b868725387e989d19d5a225634d3dbe5e69412212e34c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988535c5b5111a9bc92f3b0144cdf8a2a4", "guid": "bfdfe7dc352907fc980b868725387e9824bad605b7cce0c354b0edd3931f62c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc1bf66f505baf272cbd53faf281df7c", "guid": "bfdfe7dc352907fc980b868725387e983b0d6f7dbea939cabb53a7fd2bca8bbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b13f5b3b83ea6e08c141a4866213ef7", "guid": "bfdfe7dc352907fc980b868725387e988cc1cffd568398ae8a3a51db59be7941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec10b7ca68f89987a7bd2fccefa6c9d3", "guid": "bfdfe7dc352907fc980b868725387e980d64f4b9d5d152088c1b6df6dc4349ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850f56262462ea8772db33f14a882766f", "guid": "bfdfe7dc352907fc980b868725387e98bf1aa3f34398770580e951ea912697f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3d95dd5233446bb1fd4f192e19dece", "guid": "bfdfe7dc352907fc980b868725387e9850b583bbe43e4683b5e6ff8269fc7ae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982761ea120c395c3c41db866ec1e2f70a", "guid": "bfdfe7dc352907fc980b868725387e98be1d423f475c22238ed0f0623a693271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde282c47f52f39456d949e6be720ccf", "guid": "bfdfe7dc352907fc980b868725387e987c7ff8926f117eb25b26a9ec4d044828"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5bc913677a60bf9e25b55383ebbc3a0", "guid": "bfdfe7dc352907fc980b868725387e98b3086c1d690d46f9ab0f04913fb8b9ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af88200e8a7b1e5493f2dc4dbeab93fa", "guid": "bfdfe7dc352907fc980b868725387e98291478af2f72c6fec983212a3d649adb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c979b888e80302cd30498e8e10ca6568", "guid": "bfdfe7dc352907fc980b868725387e982ea74a583f588432049907310a2f4b08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2ab143fb1f6b1951f68b20713f56e9c", "guid": "bfdfe7dc352907fc980b868725387e988fb3e02880638ea1a00183712783a978"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d13d4ff6907228080598042a44ea29e6", "guid": "bfdfe7dc352907fc980b868725387e98966f838f4c2edddfc05b9d6b8233f030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986114b6eea8e1c7a9f71a394d0a21b6f0", "guid": "bfdfe7dc352907fc980b868725387e98efa8ef64fd9d084c090c71185c2545ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84e4544b41f44457a47c6d083d677ab", "guid": "bfdfe7dc352907fc980b868725387e98829e2237b5ff3770120a768a8e5f4370"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbccd822b1ab9174d1e568dba8468d9", "guid": "bfdfe7dc352907fc980b868725387e987521625b96fe1f57c7085846b8ba8f29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864cf48c4a9faef720a4577c5f941da4a", "guid": "bfdfe7dc352907fc980b868725387e9854328d82395ba146f2ea58015f0b51c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986863ad93abbb966e35d381fc5ee0d3e4", "guid": "bfdfe7dc352907fc980b868725387e982aaf18435e6ef22a0d278dfa0dea64a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0b83396cd1646c95ebf7a79314e970", "guid": "bfdfe7dc352907fc980b868725387e98974a73833a36d2dea10577da9b4a7e78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a570d1b7f01abda80936d5ecefede8", "guid": "bfdfe7dc352907fc980b868725387e989452f0c1bf5ee0c06cc3ab1fa8c98914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043bf8ab3655ead09c3cfdbb0d3a6fdd", "guid": "bfdfe7dc352907fc980b868725387e98b916c749320fb5c76ceed67c04aaf0ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd764ebdb01b45deed3310106b39823", "guid": "bfdfe7dc352907fc980b868725387e9815724ca83a93b5ca9ebb2a7aaf8c6b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980533461a531e1f83ef4e839927766981", "guid": "bfdfe7dc352907fc980b868725387e987d0077e2f7725b47b48b4c31bfe8c3d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847d9fe483f9eec32b88078914b31f2ce", "guid": "bfdfe7dc352907fc980b868725387e982751d1ef0729a336b6aeb7aebb5d97d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837b423e1ef640bab281de9b38773b6be", "guid": "bfdfe7dc352907fc980b868725387e98c582066d8e9de969078a62d650511b1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74dfa5c2b27b6b66deab408437bc0fb", "guid": "bfdfe7dc352907fc980b868725387e98f57e2db4711b4625a1f99e01e99298b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b5491175495218a44de1f6f4967358", "guid": "bfdfe7dc352907fc980b868725387e986a4702fd3f8b983da910a6f2bb6ef9be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ac7cc0357e71554f7ebc72be889b7d", "guid": "bfdfe7dc352907fc980b868725387e982ed3475c6b3ecc8f6bbae9e2be393c47"}], "guid": "bfdfe7dc352907fc980b868725387e9854b7d0b2a62c69e942f4cacbb0d556f8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838c8338d605a957fac7f1c411a707720", "guid": "bfdfe7dc352907fc980b868725387e98eaee970e31b5abfc45132245f3c84f6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae744aed32e6cf1b294563a02580c2f", "guid": "bfdfe7dc352907fc980b868725387e98a0c23ae655c6fc0b44b3448f0555b85b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c23021c50cdd2324e0fb4f76078d84d", "guid": "bfdfe7dc352907fc980b868725387e98acefa50799f32a5e68f0c98829ec8ca9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861c551b985f56ddbc51b083ce1116aeb", "guid": "bfdfe7dc352907fc980b868725387e986ab3b0f320a7262d6e4ce334c60c7890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a1f93a07b4ac3523e80e5dbbaeee586", "guid": "bfdfe7dc352907fc980b868725387e9888c97e71cc34607a3b1c1c80e532edf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f6c5258315349a557ece50f74e01499", "guid": "bfdfe7dc352907fc980b868725387e9820899c0247f3b73c55208d273e6b23c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8b817fdffa4552bf993bda01e7793ff", "guid": "bfdfe7dc352907fc980b868725387e9816ea97578d02c3a3c9f4a325324eafe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee3b37eab9583161096f16d3bb9eb079", "guid": "bfdfe7dc352907fc980b868725387e98de989713bc578d3442bbb1d14e370bb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866888cef55998332a9c9ec23aceebfc5", "guid": "bfdfe7dc352907fc980b868725387e98aa49cc17f50cf3816c2153bf5a57da41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff2dcddce510b172e30a5481920f493", "guid": "bfdfe7dc352907fc980b868725387e980ae622ff7a8cefd36c57d466db611457"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31c1e768a7cda2de842824689904c90", "guid": "bfdfe7dc352907fc980b868725387e98db771d3e7a8b49ba93fdb6a751df5299"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ee1f890424dba27208916ae34ea180b", "guid": "bfdfe7dc352907fc980b868725387e982d9db28b49dac68acdbc5fd4833e7300"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f14ef68a6c078f7c90777b2b7158adc", "guid": "bfdfe7dc352907fc980b868725387e986dddb2ad6a6c7a99521a07f4768a0bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b81052dd7fa4b5153a86b66dfc256f", "guid": "bfdfe7dc352907fc980b868725387e980c3bc2afd8455a2ecf03e5b9695c7e05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c8822f52fe79d52b10e122905b394e", "guid": "bfdfe7dc352907fc980b868725387e9836c7e04399b8cbee1c29483044b5bd4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98256bf92e7c766c2a8ed8223d6be685af", "guid": "bfdfe7dc352907fc980b868725387e981d3b6c9ae6f71694eb70930aea02649a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb47eb86161d77b87cb42413242668cd", "guid": "bfdfe7dc352907fc980b868725387e98217571120ea6998c4eaf065a6417582e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f1a8bb1b10592cac1df8d3cd4c12d94", "guid": "bfdfe7dc352907fc980b868725387e98f891109f3eb667d6324c07d251bc6f88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c2c1c222e5dc4c0577087f2cdf8690", "guid": "bfdfe7dc352907fc980b868725387e98f9c1618cbc013231fab7c372903fa1ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e763ce5a4da8103081c3a4e5b7a265c", "guid": "bfdfe7dc352907fc980b868725387e987be373ff96e9ec3d4fc9b3b1a42dfc5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886cd7d946b1d93d9ad79bf0a7b4b547e", "guid": "bfdfe7dc352907fc980b868725387e98ebd0167dff059346681ebe40cc446035"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983930d6411063dabd28e3841d555c85c6", "guid": "bfdfe7dc352907fc980b868725387e98ad3d3b61e4b6f513669df9b2bc74ea46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebd23b6984d3c7d39cf8145981077210", "guid": "bfdfe7dc352907fc980b868725387e98e75489e965de015eef15328d7c45f7a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98654a9689802dbea77fe53044319a3dd3", "guid": "bfdfe7dc352907fc980b868725387e98b5aacac3ffd6b0327bbcc23e37a198c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3410d1893501e598a08330700cdcbe6", "guid": "bfdfe7dc352907fc980b868725387e98f0a7b3ca68ad6e3a631439574388d1cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5c79938c134db5d61fb5687f1bfabc", "guid": "bfdfe7dc352907fc980b868725387e98b77431a64ef6a431a37c864a95e5af8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847752ef4c6cc63850c3eeec04118a419", "guid": "bfdfe7dc352907fc980b868725387e98efd8b57ab0bb0c55289522d9c27e261f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7763bf8fb198b95a751ed7f3b8dcc97", "guid": "bfdfe7dc352907fc980b868725387e9853174e4400ac787e47200fb28567f997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807b1f619fd6e2a182d676825391e90dc", "guid": "bfdfe7dc352907fc980b868725387e9895318bf081929265413bfa84cfed1e2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193a302485bcc0fbc00cde3e67cfa1a8", "guid": "bfdfe7dc352907fc980b868725387e98e6900afd581de1f746c463d128dfe1ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987954a8202639a30e7d8f8089e705692d", "guid": "bfdfe7dc352907fc980b868725387e983362fb20ed3543c9b43a16cae66ed18a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a12238bd5b9ef0a970ae98720ffd923", "guid": "bfdfe7dc352907fc980b868725387e980b42ffb47a73eb64e37aaa6e18021704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da94e096a40a4fc28dec81a718d5bb23", "guid": "bfdfe7dc352907fc980b868725387e98b558bd58500c9267dc82f69f07d4fe19"}], "guid": "bfdfe7dc352907fc980b868725387e9871417adc006c6de36a59697460882f01", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98ba4813795e610f5599c376669114d825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3dc52c9b90b9555e28483e47b792ae", "guid": "bfdfe7dc352907fc980b868725387e98dbe92c45d5761dec497265ec539aaf8e"}], "guid": "bfdfe7dc352907fc980b868725387e98bf3ac7e0b19771eb399f469661a43d6d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d621bd2aefd629bfede77fbd4e8e35aa", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98c2390de9490f0fab9fa8d57afea57fd2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}