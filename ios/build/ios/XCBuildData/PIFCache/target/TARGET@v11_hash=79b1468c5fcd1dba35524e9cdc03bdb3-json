{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8251d62234a924303a023085d955930", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9843285fec8493109b2d40ca22e06f201b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9881025673ac590377b88c4b7b996f1466", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db3cbec665f4ab9133ca21f581cd1ec7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9881025673ac590377b88c4b7b996f1466", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989d8c40edaa668bf9ba026162388d2397", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98126548c4ae1c561b189dbe7b00ac7948", "guid": "bfdfe7dc352907fc980b868725387e98a7df6916d34783c8e16d95b00960e392", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9813378f955b7ec7166b307757eb8ba9e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9810351f84bcf3df7230f5faefde7963fd", "guid": "bfdfe7dc352907fc980b868725387e9848fb529852d02aa4a057a77107a616f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e40e0760a60acc00c8898c826a5c42c", "guid": "bfdfe7dc352907fc980b868725387e98ef66152aad8a88df26d450ba095635d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8afc9810bc6fce974cf0491cbc5f47d", "guid": "bfdfe7dc352907fc980b868725387e98fccc912277ff758be4ba59fccd84bb32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cff8a4d6cc9330a9b47f0d3b146f760b", "guid": "bfdfe7dc352907fc980b868725387e98961a2e6b9b1b25d5a570cb4a695360d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d50c3279e95681d97cc3ead8e1fb3c6a", "guid": "bfdfe7dc352907fc980b868725387e9898f4b2b0c81cfc8b7c42583f07b7f456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985171f66fe132eaeb5f189efecb0803fb", "guid": "bfdfe7dc352907fc980b868725387e98a53280a9daba6a50e58b1e1021fb072f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0acd6f03e5d820a7fc272d9a2c0dc7", "guid": "bfdfe7dc352907fc980b868725387e9864f73955e5c1fb62762994b67690c402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d1ca117f6de009f15149aef479549f9", "guid": "bfdfe7dc352907fc980b868725387e984e821ca4b20b4bbb8fe902a104465ad7"}], "guid": "bfdfe7dc352907fc980b868725387e98978528a47c573a1eaf44c00f6aeb66fb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e981a134ca88c6d51f1980fcef93ed28f35"}], "guid": "bfdfe7dc352907fc980b868725387e981fbb5b005db184b8c205e7dd28e3101a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c73d492c50517c65d42c09497c2714a9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9863397a73468de8eb61b23cd9f8f91a11", "name": "FirebaseMessagingInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e98141d7db37e1ba5fe325a4a19cf71e38c", "name": "FirebaseFunctions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98422c2b3f76d0bdf719933c892e25e1fa", "name": "FirebaseFunctions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}