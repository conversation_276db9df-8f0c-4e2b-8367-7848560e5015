{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da3bd4e7f3f0a5cdf59222e3835a4b83", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986b826749d9f8e75ee90c17a525ad3553", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832fbe30ff5f39ce94f9f388b85d86465", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a7c1922a6ea412e7d60321f921b91871", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832fbe30ff5f39ce94f9f388b85d86465", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9884652c363080041b7b935ee1607ff9f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981370d5fbfc6852c0b956ab814912446a", "guid": "bfdfe7dc352907fc980b868725387e98a4d1c23beeaaecefd7b8dfde6e6b8157", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9819daa4e552f622fcba0c98f2d4c3f25a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e86adca7c4501d1b2284c4138e0e02f", "guid": "bfdfe7dc352907fc980b868725387e98f34bc2d4f6f6c69438aa0b6dfd26c77f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bfd6d3db564fbb03aa018dcb406fcd4", "guid": "bfdfe7dc352907fc980b868725387e98fddc06e9a3e345ac2800959b38190c04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af0f6c2badffc5a8c3763ea23fd897b", "guid": "bfdfe7dc352907fc980b868725387e98727087c45180e3de4aefcbec47193815"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98838ef7f89b0dd292c1781ce7d08529f6", "guid": "bfdfe7dc352907fc980b868725387e98acb5cb84a6538378df50c567dde9c86c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dbed1e75fd4b4b4bd140f6bc4d63619", "guid": "bfdfe7dc352907fc980b868725387e986e99e67f3a9e66cb3d07c9da4b8ef616"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b403d406fd3caf11b0d6ddfe6e65a02", "guid": "bfdfe7dc352907fc980b868725387e9870690381206f665df19438bcb373f9cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98039d4db29d8bc248d6d8ea4703373c4a", "guid": "bfdfe7dc352907fc980b868725387e98d708f15a8a6d47e79500672d313b323f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f373d94ad09c42d5f5de04b4717eede", "guid": "bfdfe7dc352907fc980b868725387e98944d980ac752d590b80fcb39af5963f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb674ef3c0ed60f07c3037ca7cb3fa5", "guid": "bfdfe7dc352907fc980b868725387e98f3e83530526764b72e497c503380cbb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c4d15434ca47401559b3405bbd62d4", "guid": "bfdfe7dc352907fc980b868725387e982c1032c9ae85c43442eeb3d2e512a4e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d356f3dd4ca08e4e97fef87867eb03", "guid": "bfdfe7dc352907fc980b868725387e988cbeb6cfb32d81376ace9211eded21f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef05bf32af4ef8868d59a7f41b70e03d", "guid": "bfdfe7dc352907fc980b868725387e986550924efbe4dbea37f167f63af5047e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989929dd914f079eecf2ac64d76144ff21", "guid": "bfdfe7dc352907fc980b868725387e98010f17161e153fdb2b35c54b169c4fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad31675a6233397a448e890a4818504", "guid": "bfdfe7dc352907fc980b868725387e98fe6736562e560b74eb09cec73ac2d236"}], "guid": "bfdfe7dc352907fc980b868725387e98498c2e2ddfdf51a70e932004c6aa356a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e989d96f51783ee5e1f919c2d162c41ceae"}], "guid": "bfdfe7dc352907fc980b868725387e9846a0d8868f54be321c707a10e5a510c5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98380465578e88b36d1a30a48e2f669556", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e986bf7b564f2799ef85b25ea39cad4cd18", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}