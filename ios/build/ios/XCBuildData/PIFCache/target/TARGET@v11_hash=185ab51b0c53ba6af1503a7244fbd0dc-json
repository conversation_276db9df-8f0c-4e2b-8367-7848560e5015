{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ccbb45d44afb5b3fc8d93c9c5d530d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988e5b82d983eba222d228a473f7c3ff42", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b9549023d5f94cb41e8dd02295948cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3039e4bc99d0dbb1c338eaba6e58ee5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b9549023d5f94cb41e8dd02295948cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d29bf43d43b1afcff1cdfffb66882b89", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6c338f75f8fb269a92ac1d15680d7c1", "guid": "bfdfe7dc352907fc980b868725387e984c87998358fa98de4ba95a16eccf1592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ae68746978d0a9ba6a078cafd673d0", "guid": "bfdfe7dc352907fc980b868725387e9887233153db760a02b0c1a0c8f9ae5d48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a31da2cd0fcdcd30a1219f78397b9098", "guid": "bfdfe7dc352907fc980b868725387e988268659cb65187ee5c72d8ce0aa6a47d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aadd277260b72349c7e0f044815aa0ec", "guid": "bfdfe7dc352907fc980b868725387e980db439442d49e69f617411a65e5346d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bdc7e15e01bce764dcc544bc2750008", "guid": "bfdfe7dc352907fc980b868725387e980d10c81bb6473ad9d29d742f5a5b00af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988136221be062130af3a6c5e9f5109153", "guid": "bfdfe7dc352907fc980b868725387e983cfb8335cceb9a455e8d604471a1b109"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822998dac0423001677e714a1c8931b39", "guid": "bfdfe7dc352907fc980b868725387e9834ed6ae1322f8e16b2ca8253103fd296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ded1d9c5de0c15e71816a8fd84359f", "guid": "bfdfe7dc352907fc980b868725387e987334bc2c6a8b949ab567e76055350a00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53f78d77e4000625f4f8314cc8449d5", "guid": "bfdfe7dc352907fc980b868725387e980ca2978b8799aba149c5237988c8a1b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8b715fd64457d30a9c8a7b3cda148d", "guid": "bfdfe7dc352907fc980b868725387e9840296db81170652f26f3ee7300d23114"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aedaa29aad840ea3ad4aff6ba5980bc", "guid": "bfdfe7dc352907fc980b868725387e980b9af5148b104d26a20e49ed698f65e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813553248ce59c0b5e92681aa5c3501c", "guid": "bfdfe7dc352907fc980b868725387e987d32ba95ebf77df000f0faed65569918", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575c5d02e7d0667fd6e3ecd3836d4e9b", "guid": "bfdfe7dc352907fc980b868725387e98cb6302b6fe93b2e1ed65ea4b29ea03d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da66e4d17bd856eef4f71fe030f640b9", "guid": "bfdfe7dc352907fc980b868725387e9897e3a5728609da4a1fa635278caf69a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da079cb89e373774988f8ac1f99d923d", "guid": "bfdfe7dc352907fc980b868725387e989c40c4c1531c3e9dd83aed76ba8a4eca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f62f1dff9676e504225dc7d4c1d0ee", "guid": "bfdfe7dc352907fc980b868725387e98f3a1ddd9bee4a126e94ccdff55da4b6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f840a66018150adc754a739188f5719b", "guid": "bfdfe7dc352907fc980b868725387e9808170284229c56fef2bc0083d8d792e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98630f1978d2788b939632ae9047faf20c", "guid": "bfdfe7dc352907fc980b868725387e98f1f02ab7c341ca25578852e69d4f8553", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf520fda4c8210cdefe75ddd666f01b9", "guid": "bfdfe7dc352907fc980b868725387e98a93a30f2c3a413f3882b85037a569557", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876dd4d26c2bf85ba5d460ee0c2ecf065", "guid": "bfdfe7dc352907fc980b868725387e985e9da3d5061c1e17c4b663f6135db9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d1fa5897f33b879eaf3fc1f3b7a9ce", "guid": "bfdfe7dc352907fc980b868725387e98ee78409474476064911107d034846c80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987acf7664a816a02ee8cf27e371ca52f9", "guid": "bfdfe7dc352907fc980b868725387e986e034179b90de7104c24c47afbb5825e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f25d689948e974a52dbb4118a3e0920", "guid": "bfdfe7dc352907fc980b868725387e98162ae4628f086f0f97bbb6c0d227cd03", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f82faf30f56f5829e28e7eeba6a34045", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98801bb40b5aa18ec1b8543294c8baac09", "guid": "bfdfe7dc352907fc980b868725387e98a7dd61f54cb9d01241d03e1fd1f089aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0db7036c9af555ccf690e3e27e01d2", "guid": "bfdfe7dc352907fc980b868725387e98220c2b4e8e730d6043ff8f380950e037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839ad230b91d3b62c79a53b4f638cceb4", "guid": "bfdfe7dc352907fc980b868725387e98c29a370e249c22b4255f824e2ed60f51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9982169d4a1930eb17d5b3912629575", "guid": "bfdfe7dc352907fc980b868725387e983d39abd37a5ab27cec800cec28fb9e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a8cc5d416329779e457a9372941bfb", "guid": "bfdfe7dc352907fc980b868725387e9821dfad936cd2575a8f52c01ab49a8dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1926e4c7d785cfcc1c76084e74a429a", "guid": "bfdfe7dc352907fc980b868725387e9812fea0be13b1f4f2b9f7e8f636f69160"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985121ee57f04c13e36bdced4df74d1cc2", "guid": "bfdfe7dc352907fc980b868725387e98a49581111183c206856d4c7c2b62a1be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674823ecd4fadfda2d06bd0a665d085c", "guid": "bfdfe7dc352907fc980b868725387e981283b84648b6d0c9f051aada88d0a638"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853cafd17a46d59835396db9bc9601710", "guid": "bfdfe7dc352907fc980b868725387e985dc3ec95e4db749be00e3af587a990fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f805261278406624c008f33f55134b47", "guid": "bfdfe7dc352907fc980b868725387e989588d0c2f1ee44dd79b07ad34c03e55b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817594f0b77b77f920a850b7c791f1b5f", "guid": "bfdfe7dc352907fc980b868725387e981d55d10970e9d096ec79b8c78b236a9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a3d38925b4e27e3c2aaee6aab747be8", "guid": "bfdfe7dc352907fc980b868725387e989229291fa4b3ccd7ed735ff5eb4969f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5dc5ced78a991dc9ccdd9470500461e", "guid": "bfdfe7dc352907fc980b868725387e987ce35b42573ea91ad263ae6e600fcdc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7417653aca9f9164ddac4c1edfd70ad", "guid": "bfdfe7dc352907fc980b868725387e9888cdc1fb836547d5dbda10ba3f138b4d"}], "guid": "bfdfe7dc352907fc980b868725387e9867cbbcc33f54e738ac26828e6a6a9544", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e982e86e117c9652641e61f39af195cc2be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5fae1b72688673487ee184fe608cef", "guid": "bfdfe7dc352907fc980b868725387e9875fb69c9e16333904f99daa514407a42"}], "guid": "bfdfe7dc352907fc980b868725387e98d1bf71db15e1e51ab46145b24003bced", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9863f42c3fb271f448f2b1527af6aa3380", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98fd4fea6db97a228b79d897b4b7d547a3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}