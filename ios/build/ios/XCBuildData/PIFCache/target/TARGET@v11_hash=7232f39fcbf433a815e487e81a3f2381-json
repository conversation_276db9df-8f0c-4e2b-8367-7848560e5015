{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef7ea13a8606be577ada2657f0780583", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9841460123e5c7e8847071fb1612f31005", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a67447ad8a4dc9e8147be17ddbd9a22f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98075bb58718cbb4226e000603e9effc4c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a67447ad8a4dc9e8147be17ddbd9a22f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e552683f4e12838e5b86eb01b9f1b768", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b85a1f8c8c7e4b9bfb1f83ad0caa6835", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b073de9c2eb05308acd9c16bdf3db883", "guid": "bfdfe7dc352907fc980b868725387e98a8a04a21867b54c3853dbaa4c170a7b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98872c466fa3c05c7b16521a4f121bb1cb", "guid": "bfdfe7dc352907fc980b868725387e983ab27bafb534a166be34862582758641", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb714d8aeb8fe20d1623694e3dbed2b8", "guid": "bfdfe7dc352907fc980b868725387e986d1b887bc9088303219309934dd7196f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ab322d1f32534ae75ffc465dd9e029", "guid": "bfdfe7dc352907fc980b868725387e98bc2f7755d0518141e6c9be58034edfef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980037b545c21b24fa55b4c004a492aea8", "guid": "bfdfe7dc352907fc980b868725387e98e8248f23390abb1ab36be3960b12a19b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6c6327821a01b4c7a81678bf253dadf", "guid": "bfdfe7dc352907fc980b868725387e98f1232094bd5eae912fa8885f3dfe9c09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98391425e2dc37349a6801d2314eea174f", "guid": "bfdfe7dc352907fc980b868725387e982ff947af4153c6f16bf0106c26a18b31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d03455cd81f0bfcc1fae28fe1e22c46", "guid": "bfdfe7dc352907fc980b868725387e98ff69d22b561452c278fe57e84f30530d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ce59309087f1a8cef410a283e5a37f3", "guid": "bfdfe7dc352907fc980b868725387e989ab7b2840851b7e7c5b49f2306923951", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5199a7a0e97bade5861e71668fd4111", "guid": "bfdfe7dc352907fc980b868725387e98882c78a8762905f76d7cf44fb49ccd31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e5d0a43c261dd783205adbc2bb22a0e", "guid": "bfdfe7dc352907fc980b868725387e98dbb891c3aa70a28435b686adb55bae7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf8e4ef3dcc46f7aa6b0c32420a269f", "guid": "bfdfe7dc352907fc980b868725387e98356054edfb4b6f100ae5e4c236d7b526", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8eca59c020306bd16a1700a5bf2a800", "guid": "bfdfe7dc352907fc980b868725387e989386e4bd10f46f97eb6ec10042b89eec", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9894125484a8de3e426c0020af19b46f15", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a651a29b49407cee889ba93b28fb409", "guid": "bfdfe7dc352907fc980b868725387e98d5b2053313155179512c2cf524b6e4bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d93b1d8cbe7ed39ebdfceb7dac679789", "guid": "bfdfe7dc352907fc980b868725387e981888b11f0b1033e3b7999fdad5d8b72f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892fdd5fbfa73ed078248c17ebc763f09", "guid": "bfdfe7dc352907fc980b868725387e984b68be592e23250b2c31ca4b42621601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833babf01b40f51625cb04f0c050391ab", "guid": "bfdfe7dc352907fc980b868725387e986c6199339496b0da767e9e0c81697670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a300396e7042c093030f97e1c5987cdd", "guid": "bfdfe7dc352907fc980b868725387e98b6db5ce7a5c4725e0e82bd3043a664bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa5383f26776eaa78cbf320a2ea9ff78", "guid": "bfdfe7dc352907fc980b868725387e985c5d022965e3722acc6dab4d21de3358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1c544bb739a9ccd4cde6bd188042050", "guid": "bfdfe7dc352907fc980b868725387e988210723f567f313000de5b08c1f47cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfb582a080eb66af8d7c650987a6a70f", "guid": "bfdfe7dc352907fc980b868725387e9899231e672454ef9d4e961544e0baeecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859400e70f99f6a42557a49994537a49c", "guid": "bfdfe7dc352907fc980b868725387e981441c73b884bdec305bc75cb60745b21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980004d74b71e5e026360f52555e43b3ac", "guid": "bfdfe7dc352907fc980b868725387e98c3685f5c9a899a98bdb0365c4e0550ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a7fcea709daeb24c95d65c9a63c3eba", "guid": "bfdfe7dc352907fc980b868725387e98dc7ff48f257c31e8a30a98ad216cff5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c569a6c2d284b1f50cd7059efe4d658", "guid": "bfdfe7dc352907fc980b868725387e989e967a294827e914393f373a4af50eab"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}