{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ccbb45d44afb5b3fc8d93c9c5d530d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816e02089a088797d2cdf097a17e0651d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b9549023d5f94cb41e8dd02295948cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9815ef32752fc8ab52d6f74e714636cd89", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b9549023d5f94cb41e8dd02295948cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986302cc47e015da278b3871fc302c9119", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6c338f75f8fb269a92ac1d15680d7c1", "guid": "bfdfe7dc352907fc980b868725387e98d39c8d5df29fb2f086a89d089dccd548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ae68746978d0a9ba6a078cafd673d0", "guid": "bfdfe7dc352907fc980b868725387e98a4e531d1815c8f085488f2e78609a376", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a31da2cd0fcdcd30a1219f78397b9098", "guid": "bfdfe7dc352907fc980b868725387e984cd0689bb5f7a71d0e8f680dc9487a25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aadd277260b72349c7e0f044815aa0ec", "guid": "bfdfe7dc352907fc980b868725387e9800ddb17d86d004f6611bd365e042f452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bdc7e15e01bce764dcc544bc2750008", "guid": "bfdfe7dc352907fc980b868725387e98f1c3bdffda64f6de625d43e12eb71de7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988136221be062130af3a6c5e9f5109153", "guid": "bfdfe7dc352907fc980b868725387e9870edb1548b8255bed3db77a052134328"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822998dac0423001677e714a1c8931b39", "guid": "bfdfe7dc352907fc980b868725387e981cbb115e4053d55860f62cb665b3e1e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ded1d9c5de0c15e71816a8fd84359f", "guid": "bfdfe7dc352907fc980b868725387e98d2489055bc09deb027fd58f53a1a8b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53f78d77e4000625f4f8314cc8449d5", "guid": "bfdfe7dc352907fc980b868725387e98ef559794aec1146f8fa1917ccdae9d8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8b715fd64457d30a9c8a7b3cda148d", "guid": "bfdfe7dc352907fc980b868725387e98c255a593bfc9a1f6fb33b4e69b654582"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aedaa29aad840ea3ad4aff6ba5980bc", "guid": "bfdfe7dc352907fc980b868725387e985794abecd891c344663d0ef2cdc36d47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813553248ce59c0b5e92681aa5c3501c", "guid": "bfdfe7dc352907fc980b868725387e98055c55a14c447c6d9acdc58cce424e5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575c5d02e7d0667fd6e3ecd3836d4e9b", "guid": "bfdfe7dc352907fc980b868725387e983299f828fdd33896bd1401a50efd9b4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da66e4d17bd856eef4f71fe030f640b9", "guid": "bfdfe7dc352907fc980b868725387e98bc4d644e4cc586e000d6d48e97a257f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da079cb89e373774988f8ac1f99d923d", "guid": "bfdfe7dc352907fc980b868725387e9887c49ef701ab9e228de5c7a6dc063ddf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f62f1dff9676e504225dc7d4c1d0ee", "guid": "bfdfe7dc352907fc980b868725387e984d8ee4fd2522cbbd9b435a7e3674e448"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f840a66018150adc754a739188f5719b", "guid": "bfdfe7dc352907fc980b868725387e9855cb733ffccd7b8e320193734257ab6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98630f1978d2788b939632ae9047faf20c", "guid": "bfdfe7dc352907fc980b868725387e98cd37e566c51da50ddd9133d0b2fb137c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf520fda4c8210cdefe75ddd666f01b9", "guid": "bfdfe7dc352907fc980b868725387e98898ac02da55afad21624dbe3e459ba20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876dd4d26c2bf85ba5d460ee0c2ecf065", "guid": "bfdfe7dc352907fc980b868725387e98e4ac0f48f62a7df5de28629960eb5ddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d1fa5897f33b879eaf3fc1f3b7a9ce", "guid": "bfdfe7dc352907fc980b868725387e98cda3e2cb5d385d5d55f38716a0ed2b40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987acf7664a816a02ee8cf27e371ca52f9", "guid": "bfdfe7dc352907fc980b868725387e98794b32c55a1125b7b7edb6bf7f73ecd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f25d689948e974a52dbb4118a3e0920", "guid": "bfdfe7dc352907fc980b868725387e9831bf2b04e8142e6b8ab2a46d4dac2c71", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c1b2dd81291433a2a1e00b41c73088b3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98801bb40b5aa18ec1b8543294c8baac09", "guid": "bfdfe7dc352907fc980b868725387e981852e288c82201682015253828e481b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0db7036c9af555ccf690e3e27e01d2", "guid": "bfdfe7dc352907fc980b868725387e98b14f13bc808b61019aadd11a72a9400d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839ad230b91d3b62c79a53b4f638cceb4", "guid": "bfdfe7dc352907fc980b868725387e987223420bcc492f082cb363e21aa1ebaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9982169d4a1930eb17d5b3912629575", "guid": "bfdfe7dc352907fc980b868725387e9868d700b4e4adcef23aed866df718182b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a8cc5d416329779e457a9372941bfb", "guid": "bfdfe7dc352907fc980b868725387e98396cfab1b8cc1e91112e67eba4879a7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1926e4c7d785cfcc1c76084e74a429a", "guid": "bfdfe7dc352907fc980b868725387e989328d8b5e03b0c8bcafc0a076f286118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985121ee57f04c13e36bdced4df74d1cc2", "guid": "bfdfe7dc352907fc980b868725387e980fe63ccbb41536d6f227c876db851212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674823ecd4fadfda2d06bd0a665d085c", "guid": "bfdfe7dc352907fc980b868725387e98b62422b612d667899199d7bce944cac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853cafd17a46d59835396db9bc9601710", "guid": "bfdfe7dc352907fc980b868725387e98fc92b409d0736175cd08faaa3ce24c63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f805261278406624c008f33f55134b47", "guid": "bfdfe7dc352907fc980b868725387e98af9e753fbb0664a6f88ad569dc773409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817594f0b77b77f920a850b7c791f1b5f", "guid": "bfdfe7dc352907fc980b868725387e98da6572f7216c5c3475103de15b6acc42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a3d38925b4e27e3c2aaee6aab747be8", "guid": "bfdfe7dc352907fc980b868725387e98b2eb540e7de04ef2d67697725efc3e30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5dc5ced78a991dc9ccdd9470500461e", "guid": "bfdfe7dc352907fc980b868725387e9813dfaff625f7dd697a42ec36ceb0449e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7417653aca9f9164ddac4c1edfd70ad", "guid": "bfdfe7dc352907fc980b868725387e9874c514ffb30cd17cde97cb4c3389d35e"}], "guid": "bfdfe7dc352907fc980b868725387e98096dfdf1d798da42fa1e5718998fe22b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98a9d48a0e1e99a538621327b8f14ae2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5fae1b72688673487ee184fe608cef", "guid": "bfdfe7dc352907fc980b868725387e987f291a897a7c94ffd05b669f40b4bf00"}], "guid": "bfdfe7dc352907fc980b868725387e98776845e0a35dd7f2d8c8c339835422ac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d55b2d0ceb511c5b4200c3b201757552", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e982afc28d8f70c0202e58429fd8a7901b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}