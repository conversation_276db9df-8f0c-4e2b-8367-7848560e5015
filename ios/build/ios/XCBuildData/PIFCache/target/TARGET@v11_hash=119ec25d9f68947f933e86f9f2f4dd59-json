{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1a7f1e9a07db739fa47e3c024147a74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9863b87875cba85921a69b57a3f878588f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4a1fad1a11643de9118bf055bea29e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98190b10e5dbc497c6685210fa747ddb0e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4a1fad1a11643de9118bf055bea29e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bb3ef95000bca16341381c87a3aafcc5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf0c1a374e231b470b95fafab40a46ba", "guid": "bfdfe7dc352907fc980b868725387e98f92b2de91f6a10e748285053b8dd84d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9555fca58c9219242dd0df96f6aa1e1", "guid": "bfdfe7dc352907fc980b868725387e980fd5b04791ae06323f790e68dcaba033", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e7d7cc4a0aa4af4a6c7b099c09a840e", "guid": "bfdfe7dc352907fc980b868725387e98f30352c554af62879658289c1c1a843a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e6cbebe1e70fe91b46801ccd6105db7", "guid": "bfdfe7dc352907fc980b868725387e984fcb1ba8f0bc429ff693511ac3c25473", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebe6629b52e9b654a41843ae3bd7522f", "guid": "bfdfe7dc352907fc980b868725387e98fd28d4727c0c96ff5a6da5ede8a230e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d506b1e92997c8fb683143b01143da", "guid": "bfdfe7dc352907fc980b868725387e9896eb453895cf5bc95c74e89a602b5360", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984850f6ffcffd101e494fa57f553ebe6f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980a29e80b82efe18a69f85641aa8bd28c", "guid": "bfdfe7dc352907fc980b868725387e9875318cf67b7934f7dbdcc5d7a741d2e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa01b9cad99356f6abfbe5f010f59d0b", "guid": "bfdfe7dc352907fc980b868725387e988add68a13f4c22c2d379aa025ceebcc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988488f881785e69316326cf93426e978e", "guid": "bfdfe7dc352907fc980b868725387e9803c73f84a6067d6f018641750dfbe199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb5e704b30be1577a37033c8c2e1e260", "guid": "bfdfe7dc352907fc980b868725387e9850a91f74fbe9c4d0f9ed4c712005f237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29bf42d7b28f932592c8d357aff126f", "guid": "bfdfe7dc352907fc980b868725387e987d436ed55b749b3b6e9467dbd590c241"}], "guid": "bfdfe7dc352907fc980b868725387e98b70f7e138bd76ce4e48cec8215c45c91", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e9884ece508a51d911c4614b40bfe6cf594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9187d35bfda3f41ff6bc7539a79d0", "guid": "bfdfe7dc352907fc980b868725387e9828836a5bc2ae056a786aebb5c07f84f1"}], "guid": "bfdfe7dc352907fc980b868725387e98b81c2753431712c1b56fce37d53186c4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986a49851a5fef25ebb8ea1b3ec584aab2", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e9867e35eff5cd19d51855dcab4fbf3591b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}