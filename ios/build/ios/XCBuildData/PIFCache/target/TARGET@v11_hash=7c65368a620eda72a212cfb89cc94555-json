{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f490bbe898df9b1ea371d04852e8cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9841460123e5c7e8847071fb1612f31005", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bca95ce45dd66f16f5ea67e6fee7fd38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98075bb58718cbb4226e000603e9effc4c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bca95ce45dd66f16f5ea67e6fee7fd38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e552683f4e12838e5b86eb01b9f1b768", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9842ed3acc3b84f7e5ed0404432dfc9ca9", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e40534739d777440fab8da402aa76f", "guid": "bfdfe7dc352907fc980b868725387e98a8a04a21867b54c3853dbaa4c170a7b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed5d546ac412f39e48a41f5528575a3", "guid": "bfdfe7dc352907fc980b868725387e983ab27bafb534a166be34862582758641", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7ecfdf2bea8a388b9f1af46570dcf7a", "guid": "bfdfe7dc352907fc980b868725387e986d1b887bc9088303219309934dd7196f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98294a8f96564f7dba46e08a635f77a1f2", "guid": "bfdfe7dc352907fc980b868725387e98bc2f7755d0518141e6c9be58034edfef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0db61848bc4f6b846a5c0b146c97eec", "guid": "bfdfe7dc352907fc980b868725387e98e8248f23390abb1ab36be3960b12a19b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829db4118ab87ea029b19c974cc11dedd", "guid": "bfdfe7dc352907fc980b868725387e98f1232094bd5eae912fa8885f3dfe9c09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a59a622ea5d1bec0d5551f9df5011a1e", "guid": "bfdfe7dc352907fc980b868725387e982ff947af4153c6f16bf0106c26a18b31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be6dcf549e9d88f65b32e3ce365b1b2", "guid": "bfdfe7dc352907fc980b868725387e98ff69d22b561452c278fe57e84f30530d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b050b886bad232b33cb2e7c2f4900a61", "guid": "bfdfe7dc352907fc980b868725387e989ab7b2840851b7e7c5b49f2306923951", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acb3c0c5261fad98330f6e26f987d0f1", "guid": "bfdfe7dc352907fc980b868725387e98882c78a8762905f76d7cf44fb49ccd31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801b606d7539e1c0c7a8943d67772c0bd", "guid": "bfdfe7dc352907fc980b868725387e98dbb891c3aa70a28435b686adb55bae7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc96529956a2a0a6854114eb9812eb41", "guid": "bfdfe7dc352907fc980b868725387e98356054edfb4b6f100ae5e4c236d7b526", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc9863b948a4217552d695baa48993ca", "guid": "bfdfe7dc352907fc980b868725387e989386e4bd10f46f97eb6ec10042b89eec", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987426ab0a5b69383a9a8fd2d94d74bdff", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be2ae5992e0d0c7c453df57898c889a8", "guid": "bfdfe7dc352907fc980b868725387e98d5b2053313155179512c2cf524b6e4bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7f3c8b8ccd46c9ead301f6458cb5c1d", "guid": "bfdfe7dc352907fc980b868725387e981888b11f0b1033e3b7999fdad5d8b72f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980889a644fc7fd7ec554e13c4b488208e", "guid": "bfdfe7dc352907fc980b868725387e984b68be592e23250b2c31ca4b42621601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883f0af36bbc9e65b6bd03355309498b", "guid": "bfdfe7dc352907fc980b868725387e986c6199339496b0da767e9e0c81697670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d7380be12cf7c0e7d1586c80dac4c8", "guid": "bfdfe7dc352907fc980b868725387e98b6db5ce7a5c4725e0e82bd3043a664bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f133f0c6f90b9ab6c4c633efad546dc6", "guid": "bfdfe7dc352907fc980b868725387e985c5d022965e3722acc6dab4d21de3358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988770514960a5c532d257b5136742ce91", "guid": "bfdfe7dc352907fc980b868725387e988210723f567f313000de5b08c1f47cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892e333d5711f8ae5c0c3284e299d05dc", "guid": "bfdfe7dc352907fc980b868725387e9899231e672454ef9d4e961544e0baeecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaaae32c71744fa8ef882abd6488317b", "guid": "bfdfe7dc352907fc980b868725387e981441c73b884bdec305bc75cb60745b21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d164be8fd84f64aead62f7244447be0", "guid": "bfdfe7dc352907fc980b868725387e98c3685f5c9a899a98bdb0365c4e0550ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807ddb31902917e7721d0e85ca150899a", "guid": "bfdfe7dc352907fc980b868725387e98dc7ff48f257c31e8a30a98ad216cff5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d12eaf07e94474a6f390408cda5a74", "guid": "bfdfe7dc352907fc980b868725387e989e967a294827e914393f373a4af50eab"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}