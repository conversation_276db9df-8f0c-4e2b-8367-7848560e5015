{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ac9d86ae7898eaacfbdba6a03f560aa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980640db963c071de65733346c91c47d05", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bdcb4529c589441b0eb77e87bb1dc814", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983a503f47b955cbced35971685a2d63f7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bdcb4529c589441b0eb77e87bb1dc814", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982a9bd340b08336257c189d00368e4c37", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981e95863fe07805b10909b50ee32a7d65", "guid": "bfdfe7dc352907fc980b868725387e98a5d75cce9ef987bf82238368153faccc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ba5ee1aa5a47ffd52f4af88009fe18", "guid": "bfdfe7dc352907fc980b868725387e987fbd153c7364aa1da0412c9de3c53f77", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1dff4f6a2629b036201797402df346a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b1c45c852f38c2ff066e87ffed242787", "guid": "bfdfe7dc352907fc980b868725387e9802909d34b1819a31132109f4a7710558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d8929354d5562fdd6a483cbb2491059", "guid": "bfdfe7dc352907fc980b868725387e985165046fef5b43f411938d0b2af5ecc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a24516163a334b9b0ee250ba44d7a5", "guid": "bfdfe7dc352907fc980b868725387e980bf415691a55832610f1ab4e4b763c34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7bff322abb3751a2da46db233ffb70", "guid": "bfdfe7dc352907fc980b868725387e980b5ec5228652d95e3bc21a37444d0f8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e5952f384216efc36139ab62394308", "guid": "bfdfe7dc352907fc980b868725387e98591512390b29a4b33360a8146b57db19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd2d0f893c08de30784711eb328655d2", "guid": "bfdfe7dc352907fc980b868725387e983e15af81ad4a0bee30e95f3d79e8b7b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf55650b96be473f1c98c8726dcf952f", "guid": "bfdfe7dc352907fc980b868725387e98fdcd310f35e2a45d71193fb20d4ed1ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccf517b605f9619598997e3428e0c8e6", "guid": "bfdfe7dc352907fc980b868725387e98a75951ebaa8ffcf1a3fb9a43b5265626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63941aad63d5ed1e9bf9ac8e25ec05f", "guid": "bfdfe7dc352907fc980b868725387e98827155111317e8e9442d758f95c12999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb88d612cf9d4994e5ea160f42c78c2b", "guid": "bfdfe7dc352907fc980b868725387e9809ca66d2e57edf9122011b784ec8e032"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98032d8a394cc1284d685d5cfd6946b894", "guid": "bfdfe7dc352907fc980b868725387e980da3a62867837b201b8e9584dbf26c05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98949fc58185924a40076503c546afba5c", "guid": "bfdfe7dc352907fc980b868725387e987c9b55c3bdb4ac2fdd71175a52f941fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c52cf9f83ac42d6e37be1b0f02446e8f", "guid": "bfdfe7dc352907fc980b868725387e980514a24c84cf9a35ca87ff6377778c0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dee054def533e27ac1996437e2bc95b", "guid": "bfdfe7dc352907fc980b868725387e98364c913f5cde3126bef228c3df597766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a15beaba418e9011fdde7008fc13696c", "guid": "bfdfe7dc352907fc980b868725387e982ec787f18b21b1537722afa13678e2f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa5a6bc5cbed2e7fa6a3ba7a93072e2", "guid": "bfdfe7dc352907fc980b868725387e9871fa6f9c0ad3010c6a8da5c6287b4ffa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbed03b426b352d03b2bf5bbd110fe40", "guid": "bfdfe7dc352907fc980b868725387e9856c0c42a143b6115b8b767a2b3963a1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e22cd739885f0bd78777d6bd7ad64f6", "guid": "bfdfe7dc352907fc980b868725387e98ce72516d5c737dd0be40fcacc7be1851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486f6adca9bc38db500899c06f559caf", "guid": "bfdfe7dc352907fc980b868725387e98e2e9be9d83a2ef33733416df2acb4e7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd5772f75ced7acebd6acf51c5dbbca", "guid": "bfdfe7dc352907fc980b868725387e98c303cf81b401bd121f8a9d0e282a39b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e50abb02524e49662946bbbc754c1f8", "guid": "bfdfe7dc352907fc980b868725387e98a30f5152ba1bbd3e2f8d2904822caed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1a422f1b62d125034916dcdd705863b", "guid": "bfdfe7dc352907fc980b868725387e983b8d170998490de7af5a0840358acaf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880b09abb917c2dac25e553b40663b977", "guid": "bfdfe7dc352907fc980b868725387e98e1f0e4ed8767725f0d4c35d559c1d65d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e09c4bd789a5f679d607e153c1de3b85", "guid": "bfdfe7dc352907fc980b868725387e983dae42450f7e765aff3e77a757f2f626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986161a65f00b65428ed7b79dfd43498e2", "guid": "bfdfe7dc352907fc980b868725387e98a947c611689fc4cc1f84c8d3ef75cbcb"}], "guid": "bfdfe7dc352907fc980b868725387e98eeea00923c4d4c1167e816b51302898d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98741d14d03b79aa3147d34e75a1113c63"}], "guid": "bfdfe7dc352907fc980b868725387e98feeb653131f7aca6d905fabda16c0f57", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989b595eec4f0425ce0ab1d246726858cd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}