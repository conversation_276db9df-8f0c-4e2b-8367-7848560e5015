{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ac9d86ae7898eaacfbdba6a03f560aa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984b1d7e07a56793fc14924ccdc3e1423b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bdcb4529c589441b0eb77e87bb1dc814", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e76d46bbb00a7bb64e177674f2ede82c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bdcb4529c589441b0eb77e87bb1dc814", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9821f934bced22450e5de0b201054e3a8f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981e95863fe07805b10909b50ee32a7d65", "guid": "bfdfe7dc352907fc980b868725387e987de544782d8db47218228b9b17594c8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ba5ee1aa5a47ffd52f4af88009fe18", "guid": "bfdfe7dc352907fc980b868725387e986c18c46cc1767971688774c8a6025a84", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a5e2fb3613d64a3cc9d6148b977d20f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b1c45c852f38c2ff066e87ffed242787", "guid": "bfdfe7dc352907fc980b868725387e9891847ff0ae42467b193860981ffba2f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d8929354d5562fdd6a483cbb2491059", "guid": "bfdfe7dc352907fc980b868725387e98941c07342f555a8a6433c2b1b7ab130b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a24516163a334b9b0ee250ba44d7a5", "guid": "bfdfe7dc352907fc980b868725387e98b2bc7edfb8a97b852a1fd74ac9aaead6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7bff322abb3751a2da46db233ffb70", "guid": "bfdfe7dc352907fc980b868725387e9816a17e958972b9648ee180e29ec5aa50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e5952f384216efc36139ab62394308", "guid": "bfdfe7dc352907fc980b868725387e980c9d7808968f5112ddef18ec18307bd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd2d0f893c08de30784711eb328655d2", "guid": "bfdfe7dc352907fc980b868725387e984d19c3a35cb4cae2c79dd2cdb158197b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf55650b96be473f1c98c8726dcf952f", "guid": "bfdfe7dc352907fc980b868725387e98d4258898ec3b685c08037f8a1b528add"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccf517b605f9619598997e3428e0c8e6", "guid": "bfdfe7dc352907fc980b868725387e9885a1f3492e08cda8f9f48282e32b4f55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63941aad63d5ed1e9bf9ac8e25ec05f", "guid": "bfdfe7dc352907fc980b868725387e98d0459197df66e45e627aa81604c7c8dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb88d612cf9d4994e5ea160f42c78c2b", "guid": "bfdfe7dc352907fc980b868725387e98011b335e8864ce208b18fc2321dffe4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98032d8a394cc1284d685d5cfd6946b894", "guid": "bfdfe7dc352907fc980b868725387e984bf069e42a4aaf817eae176528815090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98949fc58185924a40076503c546afba5c", "guid": "bfdfe7dc352907fc980b868725387e982120ca893719ccdc3b5117541906dc31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c52cf9f83ac42d6e37be1b0f02446e8f", "guid": "bfdfe7dc352907fc980b868725387e98cea49cdfa343040973600ce6d343145e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dee054def533e27ac1996437e2bc95b", "guid": "bfdfe7dc352907fc980b868725387e9869ae165ffb617d3aacaa8f6aab66d580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a15beaba418e9011fdde7008fc13696c", "guid": "bfdfe7dc352907fc980b868725387e986f3cc78cdbb5eeb4bf6e45b54dc8f35a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa5a6bc5cbed2e7fa6a3ba7a93072e2", "guid": "bfdfe7dc352907fc980b868725387e98638f2e899e36a7cccea3bb89b950b557"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbed03b426b352d03b2bf5bbd110fe40", "guid": "bfdfe7dc352907fc980b868725387e98629b48d390830ca6303136d4884253a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e22cd739885f0bd78777d6bd7ad64f6", "guid": "bfdfe7dc352907fc980b868725387e98da23856f9443bc13853efdfd543e47a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486f6adca9bc38db500899c06f559caf", "guid": "bfdfe7dc352907fc980b868725387e9800ab2e8c9abab67c707ce17b309ecf82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd5772f75ced7acebd6acf51c5dbbca", "guid": "bfdfe7dc352907fc980b868725387e9834ba8df4d234beb56dad59f10e844620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e50abb02524e49662946bbbc754c1f8", "guid": "bfdfe7dc352907fc980b868725387e9868fb1df7015ad8db5763906acf3d683c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1a422f1b62d125034916dcdd705863b", "guid": "bfdfe7dc352907fc980b868725387e98e5fec8c397c4e9cc07e34c9f958af996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880b09abb917c2dac25e553b40663b977", "guid": "bfdfe7dc352907fc980b868725387e98e4af43b10af84371c8fdb2857bf18ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e09c4bd789a5f679d607e153c1de3b85", "guid": "bfdfe7dc352907fc980b868725387e98a57394cd6a39f5d53f4cdb31df2c7aed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986161a65f00b65428ed7b79dfd43498e2", "guid": "bfdfe7dc352907fc980b868725387e986a1d530d377fd63087fbff23b9e0b849"}], "guid": "bfdfe7dc352907fc980b868725387e9820e9330a11aec218a0d28577cd891e2d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98df6b20501737819c5c2c7cc8fa59b3af"}], "guid": "bfdfe7dc352907fc980b868725387e9887d013e0137af551d376e288afc496d4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980692daaf7518f513b569290580d1561b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}