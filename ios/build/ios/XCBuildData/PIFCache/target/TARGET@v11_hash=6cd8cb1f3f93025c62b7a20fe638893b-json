{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984510e1480acad470f1aacaf9d798b85d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98021023c6e96a7186c4fcb00be44ad10a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872b8e31346b909c5c4b709199ce76cfb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98348ed1ae8200a5e9303b2ab971cc8e49", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872b8e31346b909c5c4b709199ce76cfb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c7e308d91bc731935fe49316bd6df4c8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9817b98a093dd20dd98b376f94b1087450", "guid": "bfdfe7dc352907fc980b868725387e9885c6b0b7cfcb58bf4b7e9a415152538f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49cccca21fa15b496a5a8476edb3a10", "guid": "bfdfe7dc352907fc980b868725387e9864ad7dda552b3f26bb244495f07a5b14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b65524ae3760b8d27f1ff3dc661edef", "guid": "bfdfe7dc352907fc980b868725387e98768994902bbddb2ff5f9ad12fe0632c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856aed3be502eeaaeab274dd2c539e4de", "guid": "bfdfe7dc352907fc980b868725387e98b70c57650ad8bbe1febf43288a7070ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5836967ea838e0956b92b794f203c3", "guid": "bfdfe7dc352907fc980b868725387e98ce08d1e461d607eb29a1f1b9c46d98f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0892723436d1a8855a1921ad1919c44", "guid": "bfdfe7dc352907fc980b868725387e98056f6b854c8000cc8559760a73776930", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5731865608c2d5978af3b65e48c94dc", "guid": "bfdfe7dc352907fc980b868725387e985046af7aa02b6c9f6807d15b00eb3bcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca182e1fac06b19823ad29a151c4b45", "guid": "bfdfe7dc352907fc980b868725387e985c83d156d0ffccbf9fbb0bf2f55f48d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b981afab67fb579a0955b3e5ce66894e", "guid": "bfdfe7dc352907fc980b868725387e98e36100487a9065e2fbcfacb7fbc4f40b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd31519d3e9ede60e972cd0650738116", "guid": "bfdfe7dc352907fc980b868725387e98edbf8c7d8f7eedb11c125337c0dcf0f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98841596a43d74341bdc5dbf5c6e09bbc2", "guid": "bfdfe7dc352907fc980b868725387e983aa237762b751706d0915caf3a458c9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980264894e72df0b91d7adfd55a76efdc0", "guid": "bfdfe7dc352907fc980b868725387e98f9a90d05d449636fee47549a2e5f153b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851900a68dcb00f1d049cda6454c54b6a", "guid": "bfdfe7dc352907fc980b868725387e9826db10a3674157b3c338497beca36c5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ab27d238780cbaa114603480d6162aa", "guid": "bfdfe7dc352907fc980b868725387e98c4eddb480fc1312986cacc27d98ce279", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec540d33c441c12fd73197a083c31334", "guid": "bfdfe7dc352907fc980b868725387e98030d855dc052df860c643ae2c21d5dfe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd1f800f6747d4594169ca66a56a4955", "guid": "bfdfe7dc352907fc980b868725387e98ebdb6340414f5ff20bb6b6938bdad4e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986058721319f54f91417fddbfbf55b136", "guid": "bfdfe7dc352907fc980b868725387e9874c1c04001f01caf755b7251c9ca2b06", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd160e08acaa4e4220b67ed4899cb220", "guid": "bfdfe7dc352907fc980b868725387e98f417a2b7455b6a48f6f5c984a6522c2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b775eef39dd54ae854dd309824700786", "guid": "bfdfe7dc352907fc980b868725387e98df60cfbe0e89b3048704602f531db2ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fd1b766da1b5612f98012b4106936ae", "guid": "bfdfe7dc352907fc980b868725387e98b28bae63a9151e24d40473c2646d2962", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98609044ed80f840a0818ad06886227515", "guid": "bfdfe7dc352907fc980b868725387e986c2c4ec50bedab46a7779fc6e02063e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98120dee978cf932457b15d219bef6374e", "guid": "bfdfe7dc352907fc980b868725387e9870d9ca3e24ad6fcc92a49870f00bfc33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bee0a68a44ae859f68ce08357017bef", "guid": "bfdfe7dc352907fc980b868725387e98edec59fd78e2cb6f4e5a25ddec91b2fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac08728db9832cbdecdf20d632686441", "guid": "bfdfe7dc352907fc980b868725387e98e6288c5e1a68a6c14cdd91c3c0b61db8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408d14897333ee9c2509134619b78443", "guid": "bfdfe7dc352907fc980b868725387e98d057e36d33dccde1db33404bfd4deec1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998e14ab799b3a02aff7bad7950ffdde", "guid": "bfdfe7dc352907fc980b868725387e985d9f1455d58851a0a7710e7ba1d816cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a4e35a4fdfd7c6ea2df447d5221af7", "guid": "bfdfe7dc352907fc980b868725387e98a9ba283c3270966cd40cd726f9188e9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2b3be022450c9cd4a324029877631cd", "guid": "bfdfe7dc352907fc980b868725387e988553db4ea96eeccc84a2695e9583c8b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b910374acd2db2dbc92048898ed39041", "guid": "bfdfe7dc352907fc980b868725387e98647821b8b0a472bef0d438bb54bd8a8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d0b41dfb9a49d722f6f070574a07e00", "guid": "bfdfe7dc352907fc980b868725387e98e9aecc1859820b9b421e39933b8afaf0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98af99f950713a3afe9b379cfc5c08fb07", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818dcb3af3784f0c9c2a7fd43b2fbce35", "guid": "bfdfe7dc352907fc980b868725387e9888ba2903ecf3684d4b07689fc64488ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fbf193d2e07ef5a37afcc9b690517b4", "guid": "bfdfe7dc352907fc980b868725387e98fb2f76649580630a24e2cce5270830b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813caedc2f98bd0f7eef9a0b29e3a5b38", "guid": "bfdfe7dc352907fc980b868725387e9815d35175dba701157fa8cae2254b7214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c2bf5b077b607946a30d62556732e8", "guid": "bfdfe7dc352907fc980b868725387e9809e8bd036f42658c71f4135f597e53b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980adee084801615e0d58c4cbeab3553bb", "guid": "bfdfe7dc352907fc980b868725387e98e8e4e40208e52b05c1b0a46e87c4b07e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984043b9b98ac2b152857e6c7cad5b2c", "guid": "bfdfe7dc352907fc980b868725387e98d60c0362f472f077d7f5efd46db6d634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8dd0c8a8aa6bfa149237f76b0990585", "guid": "bfdfe7dc352907fc980b868725387e98772a46467787c975afc7e6afd12cbaaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afab8a36ac6b1feae2d1d6ebc3b3ff01", "guid": "bfdfe7dc352907fc980b868725387e98bc381f8fb8cc38be50ec29621a5c05c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a357b7b536b875e430edeff0ea8b81", "guid": "bfdfe7dc352907fc980b868725387e98de873e0ca210b503897748cc2dc74af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed802529dc706786e54cff22837d4253", "guid": "bfdfe7dc352907fc980b868725387e98e314df6f7d52f29b0a854ad8deaa1822"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98279cf2890605142af2756178d33d5d3f", "guid": "bfdfe7dc352907fc980b868725387e98df25c3f793179349ff5b10748c574e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98224c72b97bb58bf7983b662ab0686e8b", "guid": "bfdfe7dc352907fc980b868725387e98a6097ac61e25db89d2b63e6831a5cd2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabe8205737b5f848d2631f14dd3af95", "guid": "bfdfe7dc352907fc980b868725387e98a8870eecec69554950d6265c6ee0123c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98721d8e44344b396a7b182cf2ad2dfe56", "guid": "bfdfe7dc352907fc980b868725387e984805a56a5984ca7cbdc3c03e1ec48226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d322a0c772b0ec5fc0e37147c913cb4", "guid": "bfdfe7dc352907fc980b868725387e98652fc64bd69d228ab70921945cead72c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a215c5c41bd0b3fcd27624fd3c9cbc", "guid": "bfdfe7dc352907fc980b868725387e98be322624f96739940849f2a0b2e2c751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807905c26e43fcad416e54d8a0c2e0904", "guid": "bfdfe7dc352907fc980b868725387e98ef34be3dd4475d0d0303901a2d29775e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b83bfb8f89543b3ca5dc562e25d5cb", "guid": "bfdfe7dc352907fc980b868725387e98e4196edbab1957eb844273ab6f4d0db5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808fb0c400d354c9a5dfafa4cbeea2cca", "guid": "bfdfe7dc352907fc980b868725387e98c52e6bbb7f0ad1f075abe443d7c545fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f65634f2d8417d5085e8093daab8f9b3", "guid": "bfdfe7dc352907fc980b868725387e98a222c299f21d81031f7697bcc30f272d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98384d782da33f1c57c5e07b1eb958b818", "guid": "bfdfe7dc352907fc980b868725387e9898dd39aceeee20c9a9fadc7893a22857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da2002f28eeff3a7a9d81eecef986ea", "guid": "bfdfe7dc352907fc980b868725387e9868a4c306f6fdb2f327aaefd43a25b893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edfe3b12d77f23eca028631a0a19f96f", "guid": "bfdfe7dc352907fc980b868725387e98b4a70d11b1a666e1554ce835eebdc3b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7a164fdaa7a56f988277565a2c535bc", "guid": "bfdfe7dc352907fc980b868725387e98b2296a1ad2061a81122dcddb706cf202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98417c80fec364ccfaafca1e8eba0a289f", "guid": "bfdfe7dc352907fc980b868725387e98b8f53eb6312d980e5492a92ab8a48ca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48bfc2f1cb9e64d33f096bb9390853f", "guid": "bfdfe7dc352907fc980b868725387e98e222bfb514c526911fd8b2dc9ba63ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7ed4cb068f9ece5f0ec2c3e8cc1d61", "guid": "bfdfe7dc352907fc980b868725387e98fa4fb828c9ed439a575e0f2623832945"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7774a573c58c713f3cd9df630c533a4", "guid": "bfdfe7dc352907fc980b868725387e983ad46f496c219b01e453ec01f55e0986"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c35b90c50398544c7b879557cff17fd", "guid": "bfdfe7dc352907fc980b868725387e98b6c2bf53dcbfb528870cbccc92d1d5e6"}], "guid": "bfdfe7dc352907fc980b868725387e98b9d6fd661b50c1a5db8b798c7637e308", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98fcd8afcd50d7e7dfe3306468bed4cd1e"}], "guid": "bfdfe7dc352907fc980b868725387e988cafe331e961bec2196838594cfe72a0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984e06437aee692a71ade0f0250b8b7c31", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98a21aa81e03e9c325c2c1c550b21381f0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}