{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98480cd87b970aa87661ff47f9d7e4c17d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98efc097cc4603a7dbed756e2c7b2ea30e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834f5d910d92867d12eb5c3d30409e48e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98863b00b7baf6afae54c614706546de8e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834f5d910d92867d12eb5c3d30409e48e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98647f85968e0d1778b507f1c180d04f0e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981993c3d59e44733ef55e9ebe6869817b", "guid": "bfdfe7dc352907fc980b868725387e98b087daa6e7c5f95f5f5973553e445e73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb3c2f8c3cf8a0112c1a01b90a8babbc", "guid": "bfdfe7dc352907fc980b868725387e984b09e4f3f646719d12b0ec3cbb82a078", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e33ed2f9eac5b5ec83d64ef6082b6f", "guid": "bfdfe7dc352907fc980b868725387e988957864868080fdc5098ef3c40627a7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6109f917854fb60bdb0c184271354ac", "guid": "bfdfe7dc352907fc980b868725387e98b45d2e70cdb6d97bc7ba44a8c46a3c13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf65a5cd63a7802a170a0be8f601d70", "guid": "bfdfe7dc352907fc980b868725387e98cb21ca4242a566f5487e4011565a3a9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846eec721231458c1f50a9601b40f12e0", "guid": "bfdfe7dc352907fc980b868725387e9800f2152a934a5432872d3bdff39e7e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847381ee071eb9f2f29f1ab47691efa79", "guid": "bfdfe7dc352907fc980b868725387e9887f9c9effb2b18d2d6c0bce7c1aa3d7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876df698b1509a508e7ed57b729e26d93", "guid": "bfdfe7dc352907fc980b868725387e983a8684d1d5d5fdc4a61468c386785610", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855038f5b213dba3beb3ca8f150027f6e", "guid": "bfdfe7dc352907fc980b868725387e988017a50505e91319e931f1ebf0430884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b84a03ca628d1e87f42b1358c2fae52", "guid": "bfdfe7dc352907fc980b868725387e98d31105f9e8f9266a10e2d70b0d6fc4a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e19cf797329f95ec69c668718a60f32", "guid": "bfdfe7dc352907fc980b868725387e9896153bb9eb0a270ded2a277fb5b04f98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0f7d1cb173ca73d874c3cd73f4a3a92", "guid": "bfdfe7dc352907fc980b868725387e985491e7a07c76ac785804268c3899511d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862ed700ca01abf700ea3e3f9e44b2810", "guid": "bfdfe7dc352907fc980b868725387e98eebaade30d2a29828cdc889e90b62d87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d04b8f7d55a6a0e045e1cfb30b16aee", "guid": "bfdfe7dc352907fc980b868725387e982ed7b1eb4b3fd91f3bfe24a71c0c55fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867f83347532d5c8971be2904ec732310", "guid": "bfdfe7dc352907fc980b868725387e982470904f52d7094b81e5e4c15629360f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca8d024f9e2760d612d2372906fcf43", "guid": "bfdfe7dc352907fc980b868725387e98ec41e6ccca8da30c16a6f37bec817b5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0607b8b5a37e577c2b9763797ea558f", "guid": "bfdfe7dc352907fc980b868725387e9876ee5a1f5846bdf05a6cb0e53bdba024", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895fcbea8fd84c595e902735ddd510e02", "guid": "bfdfe7dc352907fc980b868725387e98e0f27df0009ead51b4ea1dac460a9232", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5951fe125688ddfcc82ee900ea6da9f", "guid": "bfdfe7dc352907fc980b868725387e9895b5c643f89e92521e6a1c1d4ca7dcbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd3e205027cfd792fa2834c1da96ab6", "guid": "bfdfe7dc352907fc980b868725387e985d6ee5883bc3f66e04aa1b950374363f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985322d4731771756bc83dc2c569f02481", "guid": "bfdfe7dc352907fc980b868725387e981d250bb12585a511f42002b5017e7cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855872dcb8a86fccdb2386176187e44a4", "guid": "bfdfe7dc352907fc980b868725387e98f94fffa9814ebaea3bbef1f868d2a02b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d3c7db206f2054df8cabcdd6ae7d65b", "guid": "bfdfe7dc352907fc980b868725387e98161e9b7cd51282fe39bffb4579a9ba6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98971f3d6635f047ad3d85dae5b510422e", "guid": "bfdfe7dc352907fc980b868725387e9874cdf35b3e211fa96edd57e5b85db852"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b31f16ec2ac93c928fc8df05b851216", "guid": "bfdfe7dc352907fc980b868725387e98bd9a73ae776570a4bcc153ef6d8a06bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983776918b08fe0212686c3c6856803d44", "guid": "bfdfe7dc352907fc980b868725387e98be9eccb04429e7a5a6aadfcfcdf757f3"}], "guid": "bfdfe7dc352907fc980b868725387e9857fde7cc11e27ef230a03499042d261c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98588a414c4eb6f64c2f0723c6c6faed8e", "guid": "bfdfe7dc352907fc980b868725387e98bf984ba6ba850603e7871de527c9cf59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c7b35eadc6e2a0ed846c3c03e3cc71", "guid": "bfdfe7dc352907fc980b868725387e98b3bc37bce3c96ece5f61a230045eab9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987447a19c0effcc1b4f1dcc91ef3ae5bd", "guid": "bfdfe7dc352907fc980b868725387e98c09ea0831304405ec70fcd14bedaf57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef797f922df09f9e6f479815850e26b0", "guid": "bfdfe7dc352907fc980b868725387e981054c9971c16558a99080fd52936ceee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de6a935385dd8df93f24185441f74af", "guid": "bfdfe7dc352907fc980b868725387e98e1df5baa4d47435dd4ca18a20e815f7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de6fafdcd131a54a8e6d5fb58907cd4", "guid": "bfdfe7dc352907fc980b868725387e989302c6218a51537820495ae730d4755f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868d6f6a67cc6f685d88505a5ac125ab9", "guid": "bfdfe7dc352907fc980b868725387e98b0eed3bfa84986be6e4ac57ca6211596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985621bad22e3a3ee78414296f7d213fb2", "guid": "bfdfe7dc352907fc980b868725387e980c8fa74d6fc439f53e8dc3fe2446319c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98208acafec35f8a67a953fdc84a0b7e2d", "guid": "bfdfe7dc352907fc980b868725387e98dc5d9a97916da8ebad6114488b348421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cecf76f397369a6298aff806e79a6b59", "guid": "bfdfe7dc352907fc980b868725387e98db3aa4d8d7de32bd5fc4ce7ad3763a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e5000fa7f72552be819526b5dccff3", "guid": "bfdfe7dc352907fc980b868725387e985b542b1f449c5fa4677e42b0c2895157"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e2e575d36d3ca963ef162b25289a3f", "guid": "bfdfe7dc352907fc980b868725387e98288df131fbb0e04a01c32c4e09675ab3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98377af536972f937887c3cc71bca2c13a", "guid": "bfdfe7dc352907fc980b868725387e98f285d39bfe6cef999ee7b606c83f48a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98944170411f75f553894ce2c248887f5d", "guid": "bfdfe7dc352907fc980b868725387e9808dfd458896189ce99bc77af36f36509"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edbbc7f83a5237d905d8e7b7e4e1a32", "guid": "bfdfe7dc352907fc980b868725387e98999fd85f3517b00ca99ff0bee9bcc4fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d179495147cf678912a4945008f2266", "guid": "bfdfe7dc352907fc980b868725387e983b1f10c1777c2054cab6224bd13e8af9"}], "guid": "bfdfe7dc352907fc980b868725387e98a8381897336b542d52fa91d9bb21abd3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e985053a41177bc766cf55df271c992b606"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9187d35bfda3f41ff6bc7539a79d0", "guid": "bfdfe7dc352907fc980b868725387e9880f13151f964a8ff2655a6b23019ed3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3dc52c9b90b9555e28483e47b792ae", "guid": "bfdfe7dc352907fc980b868725387e988e76e6b0d93a55856e13d68e89d08ecb"}], "guid": "bfdfe7dc352907fc980b868725387e9809f41151f762e917b31a680d4b609c5f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cf0f2d3cd7e3af8d971d17bb201465a4", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e981e7b11652450534314cb7499ad17cc10", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}