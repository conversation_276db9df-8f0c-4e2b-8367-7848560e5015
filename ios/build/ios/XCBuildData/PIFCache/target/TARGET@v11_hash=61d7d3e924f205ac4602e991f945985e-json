{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eec8288cd255204067155135e8ae6fea", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1469b87dacda99a65fdf045b39bbe3a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98626e338ba2a4354cd1218627dc676663", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98299699251d6975e7c8f1b2fc84fcee2c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98626e338ba2a4354cd1218627dc676663", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c7a8c7b63fd4491c257609d5c0997486", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98504a523f4519d845336882c3f9c81380", "guid": "bfdfe7dc352907fc980b868725387e98a3127e67c4d02f4614395bfdafada93b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bef26f969b524ad277c0b88e4efc21a", "guid": "bfdfe7dc352907fc980b868725387e983989df94520e11b7c08342bacf2f8508", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987407a8b0d37c1bb91d9c712ca1da6910", "guid": "bfdfe7dc352907fc980b868725387e9841d05a4a7cad65baf7af9855dad1d764", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fad799e4316e3560b519b242a2443739", "guid": "bfdfe7dc352907fc980b868725387e983186d3c9156eacabc3b870eedc2436fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983175c1bbdf967908bace91ae283f94c3", "guid": "bfdfe7dc352907fc980b868725387e98fa98d4a0662d59c11c589acecbbb8d20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983213a433de284cbaa796e2f747fb140a", "guid": "bfdfe7dc352907fc980b868725387e986dd1fcf4a52709e13e4828b27a466c1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1e00dff6fde0f6d8ab3bfc33af22db", "guid": "bfdfe7dc352907fc980b868725387e98756a1132333af565aabad9b3cdd4eaec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbaff5a55dfe99a25a0668530b39e634", "guid": "bfdfe7dc352907fc980b868725387e980b3f3e8f7224ed517e2b60b18e3f0b1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a64e3938fe7d038467c1da440a7eb5", "guid": "bfdfe7dc352907fc980b868725387e989283637f008c25b886aa1a9f321d106b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379766a79d11476b91181cbbdc576dd6", "guid": "bfdfe7dc352907fc980b868725387e98d4c7e6bd78dfe9a02157c63799065415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d42602c6e6081b8ed79491839fc3c4", "guid": "bfdfe7dc352907fc980b868725387e980b53a958a42c1fe07c1209ff17677ec2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98936a6fff0f8ca7275d588185cadd109b", "guid": "bfdfe7dc352907fc980b868725387e9815cbde6b1a0a9419fd1543a812a4e2b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822ee20a4127252b960fbe8ab55bf6b37", "guid": "bfdfe7dc352907fc980b868725387e9818e5a5609fe94f46f279489ef44eda5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed0c71d3eaf529526fbac8fa5d84a647", "guid": "bfdfe7dc352907fc980b868725387e98eaef250af70b19573d80743dcc2d9c95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f2dea4f35a7b9ef2df8c9c34ac7817a", "guid": "bfdfe7dc352907fc980b868725387e9809bd572bd049ed0e743fe6bd43b199da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b7086fac764f9820bbb5b941ebc084", "guid": "bfdfe7dc352907fc980b868725387e982708c8cb02afc22cefb096f84b6d4dd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982da3089bd671a288e427b9b19a03ae67", "guid": "bfdfe7dc352907fc980b868725387e98a87ebc6f1e5214a7b9a77f8871af3e91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f49e2bb66b147af7a98ff23a8e2a87fc", "guid": "bfdfe7dc352907fc980b868725387e986cb756deecbfd3fc69ffee6ec7596d4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e119eda1b9e6b29123bcbff82b2f0baa", "guid": "bfdfe7dc352907fc980b868725387e9836172bb1247aa57280120ec63a758e72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876a2b2afdae5b50571e732caea85e57a", "guid": "bfdfe7dc352907fc980b868725387e98e4d6ac0a847f8dc720d9b7fa4d2cc7e4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890cc7e598116703fdd2d176ef4a519a8", "guid": "bfdfe7dc352907fc980b868725387e980bc91a8d36b3eb03a8a9951740b83c00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986657b6335b7a4a776763493b3ec70948", "guid": "bfdfe7dc352907fc980b868725387e98ef44dc1ed75b81dc03fd0e269acb34f3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980182809388d94c88b4ea07483e5dde23", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980bc6c2ae3a0560ae166f22d392a5ed82", "guid": "bfdfe7dc352907fc980b868725387e98297c24062c7e997a62c760abedcc1c51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106b4564ac6d1bbfba3279fe9098873a", "guid": "bfdfe7dc352907fc980b868725387e989f17d709c8cfc62f4aa3f0d945b74b04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fe391dbbf602154e7ee446805115ff", "guid": "bfdfe7dc352907fc980b868725387e983bbd2bb34ce486b761fa3fafdb28c5c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abfeafb988a56d95ffe6762dcc2b64fa", "guid": "bfdfe7dc352907fc980b868725387e9859e2a5c8f23c90b910077c6a423e1c5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98599815188d629216d768d6be962f7f9d", "guid": "bfdfe7dc352907fc980b868725387e987ee0cd2277d86648786d59c43fce7fb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc61748342e1d9da7797e604c8d0eb7", "guid": "bfdfe7dc352907fc980b868725387e98b1b347a62ca73ee47df342d5d07651e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e96cda6d292184c12951d752746f7a7f", "guid": "bfdfe7dc352907fc980b868725387e982e17fbc7b0d2841ae7ce2d566c61c9f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe7106620703733d435ff9ff2be3c8b5", "guid": "bfdfe7dc352907fc980b868725387e9817f414d276eae1ce6aa8b0ff73f3936e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e501f4dded1ba60c623f6029d0f5f89b", "guid": "bfdfe7dc352907fc980b868725387e985b0afe183a49969b93863d475a1451a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39ca0b1acaecd836b92803045a9d462", "guid": "bfdfe7dc352907fc980b868725387e98e604a940a0bff94923cf5e9da76dd80b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800d48a550caddfdb82ca00f7b9821625", "guid": "bfdfe7dc352907fc980b868725387e98fb1a83220dc6ffd92cd0e1da6d3db9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987944b2f70e9f5e5f003e81153f069379", "guid": "bfdfe7dc352907fc980b868725387e9836f2d727ce8fdabd3de7e299adf3d1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc72938e68c1bc2def6a340c96060a3", "guid": "bfdfe7dc352907fc980b868725387e9854a978b17f04d37306d2e9da9aadfd03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb4c4f32b59f34d2d64fa44cc05b84e", "guid": "bfdfe7dc352907fc980b868725387e98b19675db3f4674d8ad27a09f31c07770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980501630657a43668f5a8b5d7f904060f", "guid": "bfdfe7dc352907fc980b868725387e983ef253ddc86837d3240f3a3fb891cdfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1cb8e34ab5b282777878957ced53df2", "guid": "bfdfe7dc352907fc980b868725387e98fb49c146b312fe062a9bb2362942efc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cf85e6b294c10d9957a638f83bda0d8", "guid": "bfdfe7dc352907fc980b868725387e98ff1e2d17a118c240c563cff907680503"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016a2f9b5ebf0d35587c958882ce92ff", "guid": "bfdfe7dc352907fc980b868725387e98ff0e2a124a34bb744165c1416b7a0e61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6c72b2f3d95a6694419ed6546957bde", "guid": "bfdfe7dc352907fc980b868725387e983a9c2478600185590cbdf9f4af16387a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e1f7f270649dd44ad19b8141e8eb8e", "guid": "bfdfe7dc352907fc980b868725387e98b78f2046e19d880e0131c614fb4df91b"}], "guid": "bfdfe7dc352907fc980b868725387e985c588c1387d15ac538276efa7adb1fdc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860d84835ee3daa2ad1745c10477cde5e", "guid": "bfdfe7dc352907fc980b868725387e98645bcd204ecce7c8b00ea34dde7bbe50"}], "guid": "bfdfe7dc352907fc980b868725387e9857b0f371699564c8420a1f545e7917a4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9866201f3fc684f030b8a3b72c49624ba3", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e983265ba1afbc869a2002fd06a64d6f79f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}