import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/file_preview.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:firebase_storage/firebase_storage.dart';

num calculatePercentage(half, total) {
  return half / (total * 100);
}

//DIALOG  METHODS
Future<dynamic> showConfirmDeletDialog(
  BuildContext context,
  Function onDelete,
  // ProjectModel project,
) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Delete Confirmation!'),
        content: const Text('Are you sure you want to delete ?'),
        actions: <Widget>[
          // Cancel button
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Return false if cancelled
            },
            child: const Text('Cancel'),
          ),
          // Confirm button
          TextButton(
            onPressed: () {
              onDelete();
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      );
    },
  );
}

Future<dynamic> showUserConfirmDeletDialog(
  BuildContext context,
  Function onDelete,
) {
  final passwordController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Delete Confirmation!'),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: passwordController,
            decoration: InputDecoration(
              hintText: "Enter pin",
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.grey2),
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Enter password';
              }
              if (value != '1213') {
                return 'Enter correct password';
              }
              return null;
            },
            autovalidateMode: AutovalidateMode.onUserInteraction,
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Cancel
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                onDelete(); // Only called if password is correct
                Navigator.pop(context);
              }
            },
            child: const Text('Delete'),
          ),
        ],
      );
    },
  );
}

Future<dynamic> showLogoutDialog(
  BuildContext context,
  Function onDelete,
  // ProjectModel project,
) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout ?'),
        actions: <Widget>[
          // Cancel button
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Return false if cancelled
            },
            child: const Text('Cancel'),
          ),
          // Confirm button
          TextButton(
            onPressed: () {
              onDelete();
              Navigator.pop(context);
            },
            child: const Text('Logout'),
          ),
        ],
      );
    },
  );
}

Future<bool?> viewImageDialog(
  BuildContext context,
  SelectedImage? uploadImage,
  String? dbImage,
  bool isEditing,
) async {
  final res = await showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState2) {
          return Dialog(
            // backgroundColor: Colors.transparent,
            child: Container(
              width: kIsWeb ? 500 : null,
              height: kIsWeb ? 500 : null,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
              ),
              child:
                  uploadImage != null
                      ? Image.memory(
                        uploadImage.uInt8List,
                        width: double.maxFinite,
                        fit: BoxFit.cover,
                      )
                      : InteractiveViewer(
                        child: CachedNetworkImage(
                          imageUrl: dbImage ?? "",
                          width: double.maxFinite,
                          fit: BoxFit.cover,
                          placeholder:
                              (context, url) => Container(
                                width: 500,
                                height: 500,
                                alignment: Alignment.center,
                                child: const CircularProgressIndicator(
                                  color: AppColors.primary,
                                  strokeWidth: 2,
                                ),
                              ),
                          errorWidget:
                              (context, url, error) => Container(
                                width: 400,
                                height: 400,
                                color: Colors.grey[200],
                                child: const Icon(Icons.error),
                              ),
                        ),
                      ),
            ),
          );
        },
      );
    },
  );
  return res;
}

Future<dynamic> showApprovedTaskDialog(
  BuildContext context,
  Function onApprove,
  // ProjectModel project,
) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Approve Confirmation!'),
        content: const Text('Are you sure you want to approve ?'),
        actions: <Widget>[
          // Cancel button
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Return false if cancelled
            },
            child: const Text('Cancel'),
          ),
          // Confirm button
          TextButton(
            onPressed: () {
              onApprove();
              Navigator.pop(context);
            },
            child: const Text('Approve'),
          ),
        ],
      );
    },
  );
}

//FILES METHODS
String checkExtenstion(String? extenstion) {
  if (extenstion?.toLowerCase() == docTypes.PDF.toLowerCase()) {
    return docTypes.PDF;
  } else if (imageExtensions.contains(extenstion?.toLowerCase() ?? "")) {
    return docTypes.Images;
  } else {
    return docTypes.Others;
  }
}

Widget buildFilePreview({
  required BuildContext context,
  required SelectedImage? selectedFile,
  required String? dbFile,
  required String? dbFileExt,
  required String? dbFileName,
  required bool isEdit,
  required VoidCallback onDelete,
  required VoidCallback onView,
  required bool isMessage,
}) {
  if (selectedFile == null && dbFile == null) return const SizedBox();

  final extension = selectedFile?.extension?.toLowerCase();
  final fileType =
      selectedFile != null
          ? checkExtenstion(extension)
          : checkExtenstion(dbFileExt);

  return FilePreviewContainer(
    fileType: fileType,
    dbFile: dbFile,
    dbFileExt: dbFileExt,
    dbFileName: dbFileName,
    selectedFile: selectedFile,
    isEdit: isEdit,
    onDelete: onDelete,
    onView: onView,
    isMessage: isMessage,
  );
}

Future<void> viewFile({
  required BuildContext context,
  required dynamic selectedFile,
  required String? dbImg,
  required String? dbImgExt,
}) async {
  final ext = selectedFile?.extension ?? dbImgExt ?? "";
  final fileType = checkExtenstion(ext);

  if (selectedFile != null) {
    if (fileType == docTypes.PDF) {
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (_) => FileView(selectedFile: selectedFile, dbFile: null),
      //   ),
      // );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Preview not supported for this file type.')),
      );
    } else if (fileType == docTypes.Images) {
      await viewImageDialog(context, selectedFile, null, true);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Preview not supported for this file type.')),
      );
    }
  } else {
    // Fb file handling
    if (fileType == docTypes.PDF) {
      kIsWeb
          ? launchUrl(Uri.parse(dbImg ?? ""))
          : Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => Scaffold(
                    appBar: AppBar(title: Text("View PDF")),
                    body: SfPdfViewer.network(dbImg ?? ""),
                  ),
            ),
          );
    } else if (fileType == docTypes.Images) {
      await viewImageDialog(context, null, dbImg, true);
    } else {
      if (dbImg != null) {
        await launchDownloadUrl(context, dbImg);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('No download link available.')));
      }
    }
  }
}

Future<void> launchDownloadUrl(BuildContext context, String url) async {
  final Uri uri = Uri.parse(url);
  if (await canLaunchUrl(uri)) {
    await launchUrl(
      uri,
      mode: LaunchMode.externalApplication,
    ); // Opens in browser or external app
  } else {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Could not launch download URL.')));
  }
}

//COLORS METHODS
Color statusColor(String status) {
  switch (status) {
    case ProjectStatus.finished:
      return Color(0xFF1B75D0);
    case ProjectStatus.active:
      return Color(0xFF499B21);
    case ProjectStatus.onHold:
      return Color(0xFFC37B34);
    default:
      return Colors.transparent;
  }
}

Color priorityStatusColor(String status) {
  switch (status) {
    case TaskSPrioritytatus.high:
      return Color(0xFFFFB0B0);
    case TaskSPrioritytatus.mid:
      return Color(0xFFFFE1B0);
    case TaskSPrioritytatus.low:
      return Color(0xFFA4ED80);
    default:
      return Colors.transparent;
  }
}

Color getColorFromInput(String input) {
  if (input.isEmpty) return Colors.black;

  final int? number = int.tryParse(input);

  if (number != null) {
    if (number >= 1 && number <= 3) {
      return const Color(0xFFFF9963); // 1–3
    } else if (number >= 4 && number <= 7) {
      return const Color(0xFF46CE7A); // 4–7
    } else if (number >= 8 && number <= 10) {
      return const Color(0xFFB1B338); // 8–10
    } else {
      return Colors.black; // Out of range
    }
  }

  String upper = input.toUpperCase();
  int charCode = upper.codeUnitAt(0);

  if (charCode < 'A'.codeUnitAt(0) || charCode > 'Z'.codeUnitAt(0)) {
    return Colors.black;
  }

  if (charCode >= 'A'.codeUnitAt(0) && charCode <= 'I'.codeUnitAt(0)) {
    return const Color(0xFFFF9963); // A–I
  } else if (charCode >= 'J'.codeUnitAt(0) && charCode <= 'R'.codeUnitAt(0)) {
    return const Color(0xFF46CE7A); // J–R
  } else {
    return const Color(0xFFB1B338); // S–Z
  }
}

Color getDueDateColor(DateTime dueDate, bool isCompleted) {
  if (isCompleted) return Colors.black;

  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final dateOnly = DateTime(dueDate.year, dueDate.month, dueDate.day);

  if (dateOnly.isBefore(today)) {
    return AppColors.overDue;
  } else if (dateOnly.isAtSameMomentAs(today)) {
    return AppColors.today;
  } else {
    return Colors.black;
  }
}

Color getUserColor(String userId) {
  final colors = [
    Color(0xFF1E88E5), // Blue
    Color(0xFF43A047), // Green
    Color(0xFFE53935), // Red
    Color(0xFFFB8C00), // Orange
    Color(0xFF8E24AA), // Purple
    Color(0xFF00897B), // Teal
    Color(0xFF6D4C41), // Brown
    Color(0xFFD81B60), // Pink
    Color(0xFF3949AB), // Indigo
    Color(0xFF00ACC1), // Cyan
  ];

  // Hash userId to assign a consistent color
  int hash = userId.codeUnits.fold(0, (prev, elem) => prev + elem);
  return colors[hash % colors.length];
}

//STRINGS METHODS
String truncateText(String text, dynamic length, {omission = '...'}) {
  if (length >= text.length) {
    return text;
  }
  return text.replaceRange(length, text.length, omission);
}

String generateRandomId() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  final rand = Random.secure();
  return List.generate(6, (index) => chars[rand.nextInt(chars.length)]).join();
}

//DATES AND TIME METHODS
String getDateLabel(DateTime date) {
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final yesterday = today.subtract(Duration(days: 1));

  if (date == today) return 'Today';
  if (date == yesterday) return 'Yesterday';

  // Otherwise return formatted date
  return date.goodDayDate();
}

String getTimeAgo(DateTime createdAt) {
  final now = DateTime.now();
  final difference = now.difference(createdAt);

  if (difference.inMinutes < 60) {
    final minutes = difference.inMinutes;
    return '$minutes min${minutes != 1 ? 's' : ''} ago';
  } else if (difference.inHours < 24) {
    final hours = difference.inHours;
    return '$hours hour${hours != 1 ? 's' : ''} ago';
  } else {
    final formattedDate = createdAt.goodDayDate();
    return formattedDate;
  }
}

extension MetaWid on DateTime {
  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String convertToDDMMYY() {
    DateFormat formatter = DateFormat('dd-MM-yyyy');
    return formatter.format(this);
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String convertToDDMMYYYYSlashes() {
    try {
      return DateFormat('dd/MM/yyyy').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  // NEW: Combines date and time
  String goodDayDateTime() {
    try {
      return DateFormat('MMMM d, yyyy • hh:mm a').format(this);
      // Example: August 12, 2025 • 03:45 PM
    } catch (e) {
      return toString();
    }
  }
}

SettableMetadata metaDataGenerator(SelectedImage imageFile) {
  try {
    // Determine file type based on extension
    final extension = imageFile.extension?.toLowerCase() ?? '';
    final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

    if (extension == 'pdf') {
      return SettableMetadata(
        contentDisposition: 'inline',
        contentType: 'application/pdf',
      );
    } else if (imageExtensions.contains(extension)) {
      return SettableMetadata(
        contentDisposition: 'inline',
        contentType: 'image/$extension',
      );
    } else {
      return SettableMetadata();
    }
  } on Exception catch (e) {
    debugPrint(e.toString());
    return SettableMetadata();
  }
}
