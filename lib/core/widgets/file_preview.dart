import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:flutter/cupertino.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cp_associates/core/theme/app_colors.dart';

class FilePreviewContainer extends StatelessWidget {
  final String fileType;
  final String? dbFile;
  final String? dbFileName;
  final String? dbFileExt;
  final SelectedImage? selectedFile;
  final bool isEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onView;
  final bool? isMessage;

  const FilePreviewContainer({
    super.key,
    required this.fileType,
    this.dbFile,
    this.dbFileName,
    this.dbFileExt,
    this.selectedFile,
    required this.isEdit,
    this.onDelete,
    this.onView,
    this.isMessage,
  });

  @override
  Widget build(BuildContext context) {
    if (dbFile == null && selectedFile == null) return const SizedBox();

    late Widget fileDisplay;

    if (docTypes.Images == fileType) {
      fileDisplay =
          dbFile != null
              ? GestureDetector(
                onTap: onView,
                child:
                    isMessage == true
                        ? Container(
                          width: double.infinity,
                          height: 200,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CachedNetworkImage(
                              imageUrl: dbFile!,
                              placeholder:
                                  (context, url) => Container(
                                    width: double.infinity,
                                    height: 200,
                                    child: Center(
                                      child: const CircularProgressIndicator(),
                                    ),
                                  ),
                              width: double.infinity,
                              height: 200,
                              fit: BoxFit.cover,
                            ),
                          ),
                        )
                        : CachedNetworkImage(
                          imageUrl: dbFile!,
                          placeholder:
                              (context, url) => Container(
                                width: 50,
                                height: 50,
                                child: Center(
                                  child: const CircularProgressIndicator(),
                                ),
                              ),
                          width: 50,
                          height: 50,
                          fit: BoxFit.contain,
                        ),
              )
              : Image.memory(
                selectedFile!.uInt8List,
                width: 70,
                height: 70,
                fit: BoxFit.cover,
              );
    } else {
      IconData icon =
          fileType == docTypes.PDF
              ? Icons.picture_as_pdf
              : CupertinoIcons.doc_chart;

      final label =
          selectedFile != null
              ? '${selectedFile?.name}.${selectedFile?.extension}'
              : dbFileName ?? 'Unnamed File';

      fileDisplay = Container(
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),

        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.borderGrey, width: 1.5),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.grey),
            const SizedBox(height: 10),
            Text(
              truncateText(label, 10),
              style: const TextStyle(color: Colors.black, fontSize: 12),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        TransparentInkWell(onTap: onView, child: fileDisplay),
        if (isEdit)
          Positioned(
            top: 5,
            right: 5,
            child: Container(
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: AppColors.secondary,
                shape: BoxShape.circle,
              ),
              child: TransparentInkWell(
                onTap: onDelete,
                child: Icon(
                  CupertinoIcons.xmark,
                  size: 15,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
