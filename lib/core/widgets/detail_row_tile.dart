import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:flutter/material.dart';

class DetailRowTile extends StatelessWidget {
  const DetailRowTile({super.key, required this.title, required this.value});

  final String title;
  final String? value;

  @override
  Widget build(BuildContext context) {
    final MediaQueryData mediaQuery = MediaQuery.of(context);
    final double maxWidth = mediaQuery.size.width;
    dynamic length;
    if (maxWidth > 1500) {
      length = 80;
    } else if (maxWidth > 1439) {
      length = 40;
    } else {
      length = 20;
    }
    return Row(
      children: [
        Text(title, style: TextStyle(color: AppColors.grey2, fontSize: 14)),
        Spacer(),
        Text(
          truncateText(value ?? "", length) ?? "",
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }
}
