import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:flutter/material.dart';

class FilterContainer extends StatelessWidget {
  String title;
  Function() onFilterTap;
  final bool isSelected;
  FilterContainer({
    super.key,
    required this.title,
    required this.onFilterTap,
    this.isSelected = false,
  });
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onFilterTap();
      },
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 2),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
        child: Text(
          title,
          style: TextStyle(color: isSelected ? Colors.white : Colors.black),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.secondary : AppColors.chipGreyColor,
          borderRadius: BorderRadius.circular(8),

          // border: Border.all(color: AppColors.borderGrey),
        ),
      ),
    );
  }
}
