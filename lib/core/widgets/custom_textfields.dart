import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class CustomTextField extends StatelessWidget {
  CustomTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
  });

  final TextEditingController controller;
  String hintText;
  String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        Sized<PERSON>ox(height: 5),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(color: AppColors.grey2),

            // Normal border when not focused
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: AppColors.primary,
                width: 2,
              ), // or any color
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when error is shown
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused and error is shown
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Optional: default border
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return title.isEmpty ? "This field is required" : "Enter $title";
            }
            return null;
          },
        ),
      ],
    );
  }
}

class CustomNumTextField extends StatelessWidget {
  CustomNumTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
  });

  final TextEditingController controller;
  final String hintText;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            // Normal border when not focused
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: AppColors.primary,
                width: 2,
              ), // or any color
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when error is shown
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused and error is shown
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Optional: default border
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            hintText: hintText,
            hintStyle: TextStyle(
              color: AppColors.grey2,
            ), // Use your AppTextStyles.label
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Enter $title';
            }
            if (double.tryParse(value) == null) {
              return 'Enter a valid number';
            }
            return null;
          },
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }
}

class CustomFileUploadField extends StatelessWidget {
  Function onTap;
  String title;
  String hintText;
  Icon prefixIcon;
  CustomFileUploadField({
    super.key,
    required this.hintText,
    required this.title,
    required this.onTap,
    required this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        GestureDetector(
          onTap: () async => onTap(),
          child: AbsorbPointer(
            child: TextFormField(
              readOnly: true,
              decoration: InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey),
                  borderRadius: BorderRadius.circular(10),
                ),
                hintText: hintText,
                prefixIcon: prefixIcon,
                hintStyle: TextStyle(color: Colors.grey),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class CustomDateField extends StatefulWidget {
  final String? hintText;
  final String title;
  Function onTap;
  final String initialValue;
  bool? validator;

  CustomDateField({
    super.key,
    // required this.taskFormCubit,
    required this.hintText,
    required this.title,
    required this.onTap,
    required this.initialValue,
    this.validator,
  });

  @override
  State<CustomDateField> createState() => _CustomDateFieldState();
}

class _CustomDateFieldState extends State<CustomDateField> {
  // final TaskFormCubit taskFormCubit;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(widget.title),
        SizedBox(height: 5),
        TextFormField(
          readOnly: true,
          onTap: () {
            widget.onTap();
          },
          decoration: InputDecoration(
            // Normal border when not focused
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: AppColors.primary,
                width: 2,
              ), // or any color
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when error is shown
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused and error is shown
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Optional: default border
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            hintText: widget.hintText,

            suffixIcon: Icon(CupertinoIcons.calendar),

            hintStyle: TextStyle(
              color:
                  widget.initialValue.isEmpty ? AppColors.grey2 : Colors.black,
            ),
          ),
          // initialValue: widget.initialValue,
          validator: (value) {
            if (widget.validator != null) {
              if (widget.hintText == null ||
                  widget.hintText!.contains("date")) {
                return "date is required";
              }
            }

            return null;
          },
        ),
      ],
    );
  }
}

class CustomDescTextField extends StatelessWidget {
  CustomDescTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
    required this.maxChars,
    this.validator,
  });

  final TextEditingController controller;
  String hintText;
  String title;
  int maxChars;
  bool? validator;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        TextFormField(
          controller: controller,
          inputFormatters: [LengthLimitingTextInputFormatter(maxChars)],
          decoration: InputDecoration(
            // Normal border when not focused
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: AppColors.primary,
                width: 2,
              ), // or any color
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when error is shown
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Border when focused and error is shown
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(10),
            ),

            // Optional: default border
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            hintText: hintText,
            hintStyle: TextStyle(color: AppColors.grey2),
          ),
          validator: (value) {
            if (validator == true) {
              if (value == null || value.isEmpty) {
                return title.isEmpty
                    ? "this field is required"
                    : "Enter ${title}";
              }
            }
            return null;
          },
          // validator:
          //     (value) =>
          //         value == null || value.isEmpty
          //             ? 'this field is required'
          //             : null,
        ),
      ],
    );
  }
}

class CustomDropDownField extends StatelessWidget {
  CustomDropDownField({
    super.key,
    required this.title,
    required this.hintText,
    required this.initialValue,
    required this.validatorText,
    this.validator,
    required this.items,
    required this.onChanged,
  });
  String title;
  String hintText;
  dynamic initialValue;
  String validatorText;
  Function onChanged;
  bool? validator;
  List<DropdownMenuItem> items;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title),
        SizedBox(height: 5),
        DropdownButtonHideUnderline(
          child: DropdownButtonFormField(
            value: initialValue,
            validator: (value) {
              if (validator == true) {
                if (value == null || value.isEmpty) {
                  return validatorText;
                }
                return null;
              }
            },
            dropdownColor: AppColors.containerGreyColor,

            borderRadius: BorderRadius.all(Radius.circular(25)),
            elevation: 1,

            decoration: InputDecoration(
              // Normal border when not focused
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.grey2),
                borderRadius: BorderRadius.circular(10),
              ),

              // Border when focused
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColors.primary,
                  width: 2,
                ), // or any color
                borderRadius: BorderRadius.circular(10),
              ),

              // Border when error is shown
              errorBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.red),
                borderRadius: BorderRadius.circular(10),
              ),

              // Border when focused and error is shown
              focusedErrorBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.red),
                borderRadius: BorderRadius.circular(10),
              ),

              // Optional: default border
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              hintText: hintText,
              hintStyle: TextStyle(color: AppColors.grey2),
            ),
            items: items,
            onChanged: (value) => onChanged(value),
          ),
        ),
      ],
    );
  }
}

class CustomOutlineFileUploadField extends StatelessWidget {
  Function onTap;

  String hintText;
  Icon prefixIcon;
  CustomOutlineFileUploadField({
    super.key,
    required this.hintText,

    required this.onTap,
    required this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      style: OutlinedButton.styleFrom(
        textStyle: TextStyle(color: Colors.black),
        minimumSize: Size.fromHeight(50),
        side: BorderSide(color: AppColors.grey2),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      onPressed: () => onTap(),
      icon: prefixIcon,
      label: Text(hintText, style: TextStyle(color: AppColors.grey2)),
    );
  }
}


// class CustomMultiDropDownField extends StatelessWidget {
//   CustomMultiDropDownField({
//     super.key,
//     required this.title,
//     required this.hintText,
//     required this.initialValue,
//     required this.validatorText,
//     this.validator,
//     required this.items,
//     required this.controller,
//     required this.onChanged,
//   });
//   String title;
//   String hintText;
//   dynamic initialValue;
//   String validatorText;
//   Function onChanged;
//   bool? validator;
//   MultiSelectController<String>();\ controller;
//   List<DropdownItem<String>> items;
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(title),
//         SizedBox(height: 5),
//         true
//             ? MultiDropdown<String>(
//               items: items,
//               controller: controller,
//               enabled: true,
//               chipDecoration: const ChipDecoration(
//                 backgroundColor: AppColors.secondary,
//                 labelStyle: TextStyle(color: AppColors.white),
//                 wrap: true,
//                 runSpacing: 10,
//                 spacing: 10,
//               ),

//               fieldDecoration: FieldDecoration(
//                 padding: EdgeInsets.all(10),
//                 // enabledBorder: OutlineInputBorder(
//                 //   borderSide: BorderSide(
//                 //     color: Colors.grey,
//                 //   ),
//                 //   borderRadius:
//                 //       BorderRadius.circular(10),
//                 // ),
//                 hintText: "assign to",
//                 prefixIcon: const Icon(CupertinoIcons.person_2),
//                 hintStyle: TextStyle(color: Colors.grey),
//                 border: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(10),
//                   borderSide: BorderSide(color: Colors.grey),
//                 ),
//               ),
//               dropdownDecoration: const DropdownDecoration(
//                 marginTop: 2,
//                 maxHeight: 500,
//               ),
//               dropdownItemDecoration: DropdownItemDecoration(
//                 selectedIcon: const Icon(Icons.check_box, color: Colors.green),
//                 disabledIcon: Icon(Icons.lock, color: Colors.grey.shade300),
//               ),
//               validator: (value) {
//                 if (value == null || value.isEmpty) {
//                   return 'Please select a assign user';
//                 }
//                 return null;
//               },
//               onSelectionChange: (selectedItems) {
//                 projectFormCubit.updateSelectedUser(selectedItems);
//               },
//             )
//             : DropdownButtonHideUnderline(
//               child: DropdownButtonFormField(
//                 value: initialValue,
//                 validator: (value) {
//                   if (validator == true) {
//                     return null;
//                   }
//                   if (value == null || value.isEmpty) {
//                     return validatorText;
//                   }
//                   return null;
//                 },

//                 decoration: InputDecoration(
//                   // Normal border when not focused
//                   enabledBorder: OutlineInputBorder(
//                     borderSide: BorderSide(color: AppColors.grey2),
//                     borderRadius: BorderRadius.circular(10),
//                   ),

//                   // Border when focused
//                   focusedBorder: OutlineInputBorder(
//                     borderSide: BorderSide(
//                       color: AppColors.primary,
//                       width: 2,
//                     ), // or any color
//                     borderRadius: BorderRadius.circular(10),
//                   ),

//                   // Border when error is shown
//                   errorBorder: OutlineInputBorder(
//                     borderSide: BorderSide(color: Colors.red),
//                     borderRadius: BorderRadius.circular(10),
//                   ),

//                   // Border when focused and error is shown
//                   focusedErrorBorder: OutlineInputBorder(
//                     borderSide: BorderSide(color: Colors.red),
//                     borderRadius: BorderRadius.circular(10),
//                   ),

//                   // Optional: default border
//                   border: OutlineInputBorder(
//                     borderRadius: BorderRadius.circular(10),
//                   ),
//                   hintText: hintText,
//                   hintStyle: TextStyle(color: AppColors.grey2),
//                 ),
//                 items: items,
//                 onChanged: (value) => onChanged(value),
//               ),
//             ),
//       ],
//     );
//   }
// }