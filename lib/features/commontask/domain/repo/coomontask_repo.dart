import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';

abstract class CommonTaskRepo {
  Future<void> createCommonTask(CommonTaskModel commonTask);
  Future<void> updateCommonTask(CommonTaskModel commonTask);
  Future<void> deleteCommonTask(String commonTaskId);
  Future<void> completeTask(CommonTaskModel commonTask);
  Stream<List<CommonTaskModel>> getAllCommonTask();
  Future<List<CommonTaskModel>> getCompletedTask(DateTime month);
}
