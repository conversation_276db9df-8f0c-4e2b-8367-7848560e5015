import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/commontask/presentation/cubit/common_task_dart_cubit.dart';
import 'package:cp_associates/features/commontask/presentation/cubit/commontask_form_dart_cubit.dart';
import 'package:cp_associates/features/commontask/presentation/widget/common_task_form.dart';
import 'package:cp_associates/features/commontask/presentation/widget/commontask_tile.dart';
import 'package:cp_associates/features/home/<USER>/cubit/setting_cubit.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class CommonTask extends StatefulWidget {
  const CommonTask({super.key});

  @override
  State<CommonTask> createState() => _CommonTaskState();
}

class _CommonTaskState extends State<CommonTask> {
  @override
  void initState() {
    super.initState();
    final cubit = context.read<CommonTaskCubit>();
    cubit.filterTask(CommonTaskTypes.All, context);
  }

  List<DropdownMenuItem<int>> _generateMonthDropdownItems() {
    final now = DateTime.now();
    final currentMonth = now.month;
    final currentYear = now.year;

    List<DropdownMenuItem<int>> items = [];

    // Add all 12 months with proper year display
    for (int month = 1; month <= 12; month++) {
      String yearSuffix = '';

      // If month is after current month, it's from previous year
      if (month > currentMonth) {
        yearSuffix = ' ${currentYear - 1}';
      } else {
        yearSuffix = ' $currentYear';
      }

      items.add(
        DropdownMenuItem<int>(
          value: month,
          child: Text('${monthMap[month] ?? ''}$yearSuffix'),
        ),
      );
    }

    return items;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CommonTaskCubit, CommonTaskState>(
      builder: (context, state) {
        return ResponsiveWidCustom(
          mobile: Scaffold(
            appBar: AppBar(
              title: Text("Tasks", style: AppTextStyles.appBarHeading),
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),

              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  filterTabs(context, state),
                  SizedBox(height: 10),
                  if (state.selectedType == CommonTaskTypes.completed) ...[
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonHideUnderline(
                            child: DropdownButtonFormField<int>(
                              decoration: InputDecoration(
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: AppColors.grey2,
                                  ),
                                ),
                                hintText: "Select Month",
                                hintStyle: AppTextStyles.hintText,

                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              value: state.selectedMonth,
                              onChanged: (value) {
                                if (value != null) {
                                  context.read<CommonTaskCubit>().selectMonth(
                                    value,
                                  );
                                  // context
                                  //     .read<CommonTaskCubit>()
                                  //     .getMontlhyTask(
                                  //       DateTime(DateTime.now().year, value),
                                  //     );
                                }
                              },
                              items: _generateMonthDropdownItems(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                  ],

                  Expanded(
                    child: Stack(
                      children: [
                        CommonTaskTile(isMobile: true),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Container(
                              alignment: Alignment.bottomRight,
                              child: AddBtn(
                                text: "Add Task",

                                onPressed: () {
                                  showModalBottomSheet(
                                    isScrollControlled: true,
                                    context: context,
                                    builder: (context) {
                                      return Padding(
                                        padding:
                                            MediaQuery.of(context).viewInsets,
                                        child: BlocProvider(
                                          create:
                                              (context) => CommontaskFormCubit(
                                                commonTaskrepo:
                                                    context
                                                        .read<CommonTaskCubit>()
                                                        .commonTaskRepo,
                                              ),
                                          child: CommonTaskForm(
                                            editCommonTask: null,
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                                color: AppColors.secondary,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 30),
                ],
              ),
            ),
          ),
          desktop: Scaffold(
            backgroundColor: AppColors.containerGreyColor,
            appBar: PreferredSize(
              child: CommonAppBar(),
              preferredSize: Size.fromHeight(60),
            ),
            body: GestureDetector(
              onTap: () {
                final settingCubit = context.read<SettingCubit>();
                if (settingCubit.controller.isShowing) {
                  settingCubit.controller.toggle();
                } else {
                  null;
                }
              },
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 160,
                    vertical: 50,
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          InkWell(
                            onTap: () {
                              context.go(Routes.home);
                            },
                            child: Icon(Icons.arrow_back),
                          ),
                          SizedBox(width: 30),
                          Text(
                            "Common Tasks (${state.commonTasks.length})",
                            style: AppTextStyles.appBarHeading,
                          ),
                          Spacer(),
                          AddBtn(
                            text: "Add Common Task",
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 400,
                                      child: BlocProvider(
                                        create:
                                            (context) => CommontaskFormCubit(
                                              commonTaskrepo:
                                                  context
                                                      .read<CommonTaskCubit>()
                                                      .commonTaskRepo,
                                            ),
                                        child: CommonTaskForm(
                                          editCommonTask: null,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            color: AppColors.secondary,
                          ),
                        ],
                      ),
                      SizedBox(height: 30),
                      Container(
                        padding: EdgeInsets.symmetric(
                          vertical: 30,
                          horizontal: 10,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 15,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 30,
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    "All Tasks",
                                    style: AppTextStyles.appBarHeading,
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            CommonTaskTile(isMobile: false),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget filterTabs(BuildContext context, CommonTaskState state) {
    return Row(
      children: [
        FilterContainer(
          title: "All",
          onFilterTap: () {
            context.read<CommonTaskCubit>().filterTask(
              CommonTaskTypes.All,
              context,
            );
          },
          isSelected: state.selectedType == CommonTaskTypes.All,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "My Task",
          onFilterTap: () {
            context.read<CommonTaskCubit>().filterTask(
              CommonTaskTypes.mytask,
              context,
            );
          },
          isSelected: state.selectedType == CommonTaskTypes.mytask,
        ),
        SizedBox(width: 10),

        FilterContainer(
          title: "Completed",
          onFilterTap: () {
            context.read<CommonTaskCubit>().filterTask(
              CommonTaskTypes.completed,
              context,
            );
          },
          isSelected: state.selectedType == CommonTaskTypes.completed,
        ),
        SizedBox(width: 10),
      ],
    );
  }
}
