import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';
import 'package:cp_associates/features/commontask/presentation/cubit/common_task_dart_cubit.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommonTaskDetail extends StatefulWidget {
  final int index;
  final CommonTaskModel commonTask;
  final ProjectModel? project;
  const CommonTaskDetail({
    super.key,
    required this.index,
    required this.commonTask,
    required this.project,
  });

  @override
  State<CommonTaskDetail> createState() => _CommonTaskDetailState();
}

class _CommonTaskDetailState extends State<CommonTaskDetail> {
  @override
  void initState() {
    // Note: CommonTaskCubit doesn't have fetchTaskById method, so we'll remove this call
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CommonTaskCubit, CommonTaskState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        print("CommonTask Cubit build");
        // print(commonTask.title);
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 30),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  // mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: getColorFromInput(
                          (widget.index + 1).toString(),
                          // "M",
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      height: 45,
                      width: 45,
                      child: Center(
                        child: Text(
                          (widget.index + 1).toString(),
                          // "test",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(width: 15),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.commonTask.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),

                        Text(
                          widget.commonTask.desc,
                          style: TextStyle(
                            fontSize: 16,
                            // fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Icon(CupertinoIcons.xmark, size: 20),
                    ),
                    // IconButton(child: Icon(CupertinoIcons.xmark, size: 20)),
                  ],
                ),
                SizedBox(height: 20),

                PrimaryButton(
                  width: double.maxFinite,
                  text:
                      widget.commonTask.isCompleted ? "Completed" : "Complete",
                  onPressed: () {
                    context
                        .read<CommonTaskCubit>()
                        .commonTaskRepo
                        .updateCommonTask(
                          widget.commonTask.copyWith(
                            isCompleted: true,
                            completedAt: DateTime.now(),
                          ),
                        );
                    Navigator.of(context).pop();
                  },
                ),
                SizedBox(height: 10),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 48,
                        child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            side: BorderSide(color: AppColors.borderGrey),
                            backgroundColor: Colors.white,
                            elevation: 0,
                            foregroundColor: AppColors.black,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            textStyle: AppTextStyles.button,
                          ),
                          onPressed: () {},
                          label: Text("Delete"),

                          icon: Icon(CupertinoIcons.trash),
                        ),
                      ),
                    ),
                  ],
                ),
                // SizedBox(height: 30),
              ],
            ),
          );
        }
      },
    );
  }
}
