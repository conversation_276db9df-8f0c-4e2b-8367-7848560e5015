import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';
import 'package:cp_associates/features/commontask/domain/repo/coomontask_repo.dart';
import 'package:flutter/cupertino.dart';

part 'common_task_dart_state.dart';

class CommonTaskCubit extends Cubit<CommonTaskState> {
  CommonTaskRepo commonTaskRepo;
  CommonTaskCubit(this.commonTaskRepo) : super(CommonTaskState.initial());
  StreamSubscription<List<CommonTaskModel>>? commonTaskStream;

  void fetchAllCommonTask() {
    emit(state.copyWith(isLoading: true, message: ""));

    commonTaskStream?.cancel();

    commonTaskStream = commonTaskRepo.getAllCommonTask().listen(
      (commonTask) {
        print("AllCommonTaskStream-${commonTask.length}");
        commonTask.sort((a, b) {
          DateTime aDate = a.dueDate;
          DateTime bDate = b.dueDate;
          return aDate.compareTo(bDate);
        });

        emit(
          state.copyWith(
            commonTasks: commonTask,
            isLoading: false,
            message: "",
          ),
        );
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch common task: ${error.toString()}",
          ),
        );
      },
    );
  }

  void fetchCompletedCommonTask(DateTime month) {
    emit(state.copyWith(isLoading: true, message: ""));

    commonTaskRepo
        .getCompletedTask(month)
        .then((commonTask) {
          emit(
            state.copyWith(
              filteredCommonTasks: commonTask,
              isLoading: false,
              message: "",
            ),
          );
        })
        .catchError((error) {
          print(error.toString());
          emit(
            state.copyWith(
              isLoading: false,
              message: "Failed to fetch common task: ${error.toString()}",
            ),
          );
        });
  }

  // CRUD METHODS
  void completeTask(CommonTaskModel commonTask) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await commonTaskRepo.completeTask(commonTask);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Admin Task updated successfully",
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to update admin task: ${e.toString()}",
        ),
      );
    }
  }

  void deleteCommonTask(String commonTaskId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await commonTaskRepo.deleteCommonTask(commonTaskId);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Admin Task deleted successfully",
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to delete admin task: ${e.toString()}",
        ),
      );
    }
  }

  //UI METHODS
  void filterTask(String type, BuildContext context) {
    emit(state.copyWith(isLoading: true, selectedType: type, message: ""));

    if (type == CommonTaskTypes.onGoing) {
      fetchAllCommonTask();
      // final tasks =
      //     state.adminTasks.where((task) => task.isCompleted == false).toList();
      emit(
        state.copyWith(
          isLoading: false,
          message: "",
          selectedType: CommonTaskTypes.onGoing,
        ),
      );
    } else if (type == CommonTaskTypes.completed) {
      final now = DateTime.now();

      emit(state.copyWith(selectedMonth: now.month));

      emit(
        state.copyWith(
          isLoading: false,
          message: "",
          selectedType: CommonTaskTypes.completed,
        ),
      );
    }
  }

  void selectMonth(int value) {
    emit(state.copyWith(selectedMonth: value));
  }
}
