part of 'commontask_form_dart_cubit.dart';

@immutable
class CommontaskFormState {
  final String message;
  final bool isLoading;
  final String? selectedProject;
  final DateTime? dueDate;
  final TimeOfDay? dueTime;
  final String? selectedUser;

  CommontaskFormState({
    required this.message,
    required this.isLoading,
    required this.selectedProject,
    required this.dueDate,
    required this.dueTime,
    required this.selectedUser,
  });

  factory CommontaskFormState.initial() {
    return CommontaskFormState(
      message: '',
      isLoading: false,
      selectedProject: null,
      dueDate: null,
      dueTime: null,
      selectedUser: null,
    );
  }

  CommontaskFormState copyWith({
    String? message,
    bool? isLoading,
    String? selectedProject,
    DateTime? dueDate,
    TimeOfDay? dueTime,
    String? selectedUser,
  }) {
    return CommontaskFormState(
      selectedProject: selectedProject ?? this.selectedProject,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      dueDate: dueDate ?? this.dueDate,
      dueTime: dueTime ?? this.dueTime,
      selectedUser: selectedUser ?? this.selectedUser,
    );
  }
}
