part of 'common_task_dart_cubit.dart';

class CommonTaskState {
  final List<CommonTaskModel> commonTasks;
  final List<CommonTaskModel> filteredCommonTasks;
  final int selectedMonth;
  final String selectedType;
  final String message;
  final bool isLoading;

  const CommonTaskState({
    required this.commonTasks,
    required this.filteredCommonTasks,
    required this.selectedMonth,
    required this.selectedType,
    required this.message,
    required this.isLoading,
  });

  CommonTaskState copyWith({
    List<CommonTaskModel>? commonTasks,
    List<CommonTaskModel>? filteredCommonTasks,
    int? selectedMonth,
    String? selectedType,
    String? message,
    bool? isLoading,
  }) {
    return CommonTaskState(
      commonTasks: commonTasks ?? this.commonTasks,
      filteredCommonTasks: filteredCommonTasks ?? this.filteredCommonTasks,
      selectedMonth: selectedMonth ?? this.selectedMonth,
      selectedType: selectedType ?? this.selectedType,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  factory CommonTaskState.initial() {
    return CommonTaskState(
      filteredCommonTasks: [],
      selectedType: 'All',
      selectedMonth: DateTime.now().month,
      commonTasks: [],
      message: '',
      isLoading: false,
    );
  }
}
