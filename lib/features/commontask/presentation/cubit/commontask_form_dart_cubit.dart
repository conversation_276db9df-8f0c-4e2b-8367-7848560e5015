import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';
import 'package:cp_associates/features/commontask/domain/repo/coomontask_repo.dart';
import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'commontask_form_dart_state.dart';

class CommontaskFormCubit extends Cubit<CommontaskFormState> {
  CommonTaskRepo commonTaskrepo;
  CommontaskFormCubit({required this.commonTaskrepo})
    : super(CommontaskFormState.initial());

  final titleController = TextEditingController();
  final descController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Initialize form
  void initializeForm(CommonTaskModel? editCommonTask) {
    if (editCommonTask != null) {
      titleController.text = editCommonTask.title;
      descController.text = editCommonTask.desc;
      state.selectedUser = editCommonTask.assignTo;

      emit(
        state.copyWith(
          selectedProject: editCommonTask.projectId,
          dueDate: editCommonTask.dueDate,
        ),
      );
    } else {
      emit(CommontaskFormState.initial());
    }
  }

  // UI RELATED FUNCTION
  void selectProject(String projectId) {
    emit(state.copyWith(selectedProject: projectId));
  }

  void selectDueDate(BuildContext context) async {
    final res = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    emit(state.copyWith(dueDate: res));
  }

  void selectDueTime(BuildContext context) async {
    final res = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    emit(state.copyWith(dueTime: res));

    // emit(state.copyWith(dueDate: res));
  }

  void selectAssignUser(String assignUser) {
    emit(state.copyWith(selectedUser: assignUser));
  }

  // Submit
  void submit(CommonTaskModel? editCommonTask, BuildContext context) async {
    if (state.isLoading) {
      return;
    }
    if (formKey.currentState?.validate() ?? false) {
      emit(state.copyWith(isLoading: true, message: ''));
      try {
        final commonTask = CommonTaskModel(
          docId: editCommonTask?.docId ?? "",
          projectId: state.selectedProject,
          createdAt: editCommonTask?.createdAt ?? DateTime.now(),
          createdBy: FBAuth.auth.currentUser?.uid ?? "",
          assignTo: state.selectedUser ?? "",
          title: titleController.text,
          desc: descController.text,
          isCompleted: editCommonTask?.isCompleted ?? false,
          completedAt: editCommonTask?.completedAt,
          dueDate:
              state.dueDate != null
                  ? state.dueTime != null
                      ? DateTime(
                        state.dueDate?.year ?? DateTime.now().year,
                        state.dueDate?.month ?? DateTime.now().month,
                        state.dueDate?.day ?? DateTime.now().day,
                        state.dueTime?.hour ?? DateTime.now().hour,
                        state.dueTime?.minute ?? DateTime.now().minute,
                      )
                      : state.dueDate ?? DateTime.now()
                  : DateTime.now(),
        );

        if (editCommonTask == null) {
          await commonTaskrepo.createCommonTask(commonTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "New  Task created successfully",
            ),
          );
          //notify
          final user = context.read<UserCubit>().getUserById(
            commonTask.assignTo,
          );
          final notification = NotificationModel(
            docId: '', // Firestore will auto-generate
            notifyType: NotificationType.commontask,
            title: "New Common Task",
            message: "New Common Task created",
            data: {'taskId': commonTask.docId},
            token: user?.token ?? '',
            createdAt: DateTime.now(),
            users: [commonTask.assignTo],
          );
          context.read<NotificationFormCubit>().createNotification(
            notification,
          );
          Navigator.of(context).pop();
        } else {
          await commonTaskrepo.updateCommonTask(commonTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "Update  Task successfully",
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        emit(state.copyWith(isLoading: false, message: e.toString()));
      }
    }
  }
}
