import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/detail_row_tile.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';
import 'package:cp_associates/features/commontask/presentation/cubit/common_task_dart_cubit.dart';
import 'package:cp_associates/features/commontask/presentation/cubit/commontask_form_dart_cubit.dart';
import 'package:cp_associates/features/commontask/presentation/pages/common_task_detail.dart';
import 'package:cp_associates/features/commontask/presentation/widget/common_task_form.dart';

import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class CommonTaskTile extends StatefulWidget {
  const CommonTaskTile({super.key, required this.isMobile});
  final bool isMobile;

  @override
  State<CommonTaskTile> createState() => _CommonTaskTileState();
}

class _CommonTaskTileState extends State<CommonTaskTile> {
  void initState() {
    // context.read<AdminTaskCubit>().fetchAllAdminTask();
    super.initState();
  }

  // List<CommonTaskModel> _getFilteredTasks(CommonTaskState state) {
  //   final currentUserId = FBAuth.auth.currentUser?.uid ?? '';

  //   switch (state.selectedType) {
  //     case CommonTaskTypes.All:
  //       // Show all non-completed tasks
  //       return state.commonTasks;
  //     case CommonTaskTypes.mytask:
  //       // Show tasks assigned to current user
  //       return state.commonTasks
  //           .where((task) => task.assignTo == currentUserId)
  //           .toList();
  //     case CommonTaskTypes.completed:
  //       // Show completed tasks from filteredCommonTasks
  //       return state.filteredCommonTasks;
  //     default:
  //       return state.commonTasks;
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    final commonTaskCubit = context.read<CommonTaskCubit>();
    final projectCubit = context.read<ProjectCubit>();
    final userCubit = context.read<UserCubit>();
    return BlocConsumer<CommonTaskCubit, CommonTaskState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state.commonTasks.isNotEmpty) {
          List<CommonTaskModel> filteredtasks = [];
          if (state.selectedType == TaskTypes.All) {
            filteredtasks = state.commonTasks;
          } else if (state.selectedType == TaskTypes.mytask) {
            filteredtasks =
                state.commonTasks.where((task) {
                  return task.assignTo.contains(
                    FBAuth.auth.currentUser?.uid ?? "",
                  );
                }).toList();
          }
          filteredtasks.isEmpty
              ? Center(child: Text("No Common Task Avaliable"))
              : SingleChildScrollView(
                child: StaggeredGrid.extent(
                  maxCrossAxisExtent: 530,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 30,

                  children: [
                    ...List.generate(filteredtasks.length, (index) {
                      final commonTask = filteredtasks[index];
                      ProjectModel? project = projectCubit.fetchProjectById(
                        commonTask.projectId ?? "",
                      );
                      print("${commonTask.title} :${commonTask.isCompleted}");
                      return TransparentInkWell(
                        onDoubleTap: () {
                          commonTask.isCompleted
                              ? null
                              : widget.isMobile
                              ? showModalBottomSheet(
                                isScrollControlled: true,
                                context: context,
                                builder: (context) {
                                  return Padding(
                                    padding: MediaQuery.of(context).viewInsets,
                                    child: BlocProvider(
                                      create:
                                          (context) => CommontaskFormCubit(
                                            commonTaskrepo:
                                                context
                                                    .read<CommonTaskCubit>()
                                                    .commonTaskRepo,
                                          ),
                                      child: CommonTaskForm(
                                        editCommonTask: commonTask,
                                      ),
                                    ),
                                  );
                                },
                              )
                              : showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 400,
                                      child: BlocProvider(
                                        create:
                                            (context) => CommontaskFormCubit(
                                              commonTaskrepo:
                                                  context
                                                      .read<CommonTaskCubit>()
                                                      .commonTaskRepo,
                                            ),
                                        child: CommonTaskForm(
                                          editCommonTask: commonTask,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                        },
                        onTap: () {
                          if (commonTask.assignTo !=
                              FBAuth.auth.currentUser?.uid) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  "You are not assigned to this task.",
                                ),
                              ),
                            );
                          }
                          (!commonTask.isCompleted &&
                                  commonTask.assignTo ==
                                      FBAuth.auth.currentUser?.uid)
                              ? widget.isMobile
                                  ? showModalBottomSheet(
                                    isScrollControlled: true,
                                    useSafeArea: true,
                                    context: context,
                                    builder: (context) {
                                      return Padding(
                                        padding:
                                            MediaQuery.of(context).viewInsets,
                                        child: CommonTaskDetail(
                                          index: index,
                                          commonTask: commonTask,
                                          project: project,
                                        ),
                                      );
                                    },
                                  )
                                  : showDialog(
                                    context: context,
                                    builder: (context) {
                                      return Dialog(
                                        child: Container(
                                          width: 400,
                                          child: CommonTaskDetail(
                                            index: index,
                                            commonTask: commonTask,
                                            project: project,
                                          ),
                                        ),
                                      );
                                    },
                                  )
                              : null;
                        },
                        onLongPress: () {
                          showConfirmDeletDialog(context, () {
                            commonTaskCubit.commonTaskRepo.deleteCommonTask(
                              commonTask.docId,
                            );
                          });
                        },
                        child: Container(
                          padding: EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: AppColors.containerGreyColor,
                            border: Border.all(color: AppColors.borderGrey),
                            borderRadius: BorderRadius.circular(12),
                          ),

                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                // mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: getColorFromInput(
                                        (index + 1).toString(),
                                      ),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    height: 45,
                                    width: 45,
                                    child: Center(
                                      child: Text(
                                        (index + 1).toString(),
                                        // "test",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 15,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 15),

                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        commonTask.title,
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),

                                      Text(
                                        truncateText(commonTask.desc, 30),
                                        style: TextStyle(fontSize: 14),
                                      ),
                                    ],
                                  ),
                                  Spacer(),
                                  commonTask.isCompleted
                                      ? Icon(
                                        CupertinoIcons.check_mark_circled,
                                        color: Colors.green,
                                      )
                                      : Icon(
                                        CupertinoIcons.chevron_right,
                                        size: 20,
                                      ),
                                ],
                              ),
                              SizedBox(height: 15),
                              DetailRowTile(
                                title: "Project",
                                value: project?.projectTitle ?? "-",
                              ),

                              SizedBox(height: 5),
                              Row(
                                children: [
                                  Text(
                                    "Due Date",
                                    style: TextStyle(
                                      color: AppColors.grey2,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Spacer(),
                                  Text(
                                    commonTask.dueDate.hour == 0 &&
                                            commonTask.dueDate.minute == 0 &&
                                            commonTask.dueDate.second == 0
                                        ? commonTask.dueDate.goodDayDate()
                                        : commonTask.dueDate.goodDayDateTime(),

                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: getDueDateColor(
                                        commonTask.dueDate,
                                        commonTask.isCompleted,
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              SizedBox(height: 5),

                              Row(
                                children: [
                                  Text(
                                    "Created",
                                    style: TextStyle(
                                      color: AppColors.grey2,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Spacer(),
                                  Text(
                                    getTimeAgo(commonTask.createdAt),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    padding: EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      // shape: BoxShape.circle,
                                      color: AppColors.black,
                                      // borderRadius: BorderRadius.circular(4),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  SizedBox(width: 5),

                                  Text(
                                    userCubit
                                            .getUserById(commonTask.createdBy)
                                            ?.name ??
                                        "",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),

                              SizedBox(height: 5),
                              DetailRowTile(
                                title: "Assigned To",
                                value:
                                    userCubit
                                        .getUserById(commonTask.assignTo)
                                        ?.name ??
                                    "",
                              ),
                              SizedBox(height: 5),
                              commonTask.completedAt != null
                                  ? DetailRowTile(
                                    title: "Completed At",
                                    value:
                                        commonTask.completedAt
                                            ?.goodDayDateTime(),
                                  )
                                  : SizedBox(),
                            ],
                          ),
                        ),
                      );
                    }),
                    SizedBox(height: 30),
                  ],
                ),
              );
        }
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.commonTasks.isEmpty &&
            state.filteredCommonTasks.isEmpty) {
          return const Center(child: Text("No Common Task Available"));
        } else {
          return Center(child: Text("No Common Task Avaliable"));
          // List<CommonTaskModel> commonTasks = _getFilteredTasks(state);
          // return SingleChildScrollView(
          //   child: StaggeredGrid.extent(
          //     maxCrossAxisExtent: 530,
          //     mainAxisSpacing: 15,
          //     crossAxisSpacing: 30,

          //     children: [
          //       ...List.generate(commonTasks.length, (index) {
          //         final commonTask = commonTasks[index];
          //         ProjectModel? project = projectCubit.fetchProjectById(
          //           commonTask.projectId ?? "",
          //         );
          //         print("${commonTask.title} :${commonTask.isCompleted}");
          //         return TransparentInkWell(
          //           onDoubleTap: () {
          //             commonTask.isCompleted
          //                 ? null
          //                 : widget.isMobile
          //                 ? showModalBottomSheet(
          //                   isScrollControlled: true,
          //                   context: context,
          //                   builder: (context) {
          //                     return Padding(
          //                       padding: MediaQuery.of(context).viewInsets,
          //                       child: BlocProvider(
          //                         create:
          //                             (context) => CommontaskFormCubit(
          //                               commonTaskrepo:
          //                                   context
          //                                       .read<CommonTaskCubit>()
          //                                       .commonTaskRepo,
          //                             ),
          //                         child: CommonTaskForm(
          //                           editCommonTask: commonTask,
          //                         ),
          //                       ),
          //                     );
          //                   },
          //                 )
          //                 : showDialog(
          //                   context: context,
          //                   builder: (context) {
          //                     return Dialog(
          //                       child: Container(
          //                         width: 400,
          //                         child: BlocProvider(
          //                           create:
          //                               (context) => CommontaskFormCubit(
          //                                 commonTaskrepo:
          //                                     context
          //                                         .read<CommonTaskCubit>()
          //                                         .commonTaskRepo,
          //                               ),
          //                           child: CommonTaskForm(
          //                             editCommonTask: commonTask,
          //                           ),
          //                         ),
          //                       ),
          //                     );
          //                   },
          //                 );
          //           },
          //           onTap: () {
          //             if (commonTask.assignTo != FBAuth.auth.currentUser?.uid) {
          //               ScaffoldMessenger.of(context).showSnackBar(
          //                 SnackBar(
          //                   content: Text("You are not assigned to this task."),
          //                 ),
          //               );
          //             }
          //             (!commonTask.isCompleted &&
          //                     commonTask.assignTo ==
          //                         FBAuth.auth.currentUser?.uid)
          //                 ? widget.isMobile
          //                     ? showModalBottomSheet(
          //                       isScrollControlled: true,
          //                       useSafeArea: true,
          //                       context: context,
          //                       builder: (context) {
          //                         return Padding(
          //                           padding: MediaQuery.of(context).viewInsets,
          //                           child: CommonTaskDetail(
          //                             index: index,
          //                             commonTask: commonTask,
          //                             project: project,
          //                           ),
          //                         );
          //                       },
          //                     )
          //                     : showDialog(
          //                       context: context,
          //                       builder: (context) {
          //                         return Dialog(
          //                           child: Container(
          //                             width: 400,
          //                             child: CommonTaskDetail(
          //                               index: index,
          //                               commonTask: commonTask,
          //                               project: project,
          //                             ),
          //                           ),
          //                         );
          //                       },
          //                     )
          //                 : null;
          //           },
          //           onLongPress: () {
          //             showConfirmDeletDialog(context, () {
          //               commonTaskCubit.commonTaskRepo.deleteCommonTask(
          //                 commonTask.docId,
          //               );
          //             });
          //           },
          //           child: Container(
          //             padding: EdgeInsets.all(15),
          //             decoration: BoxDecoration(
          //               color: AppColors.containerGreyColor,
          //               border: Border.all(color: AppColors.borderGrey),
          //               borderRadius: BorderRadius.circular(12),
          //             ),

          //             child: Column(
          //               crossAxisAlignment: CrossAxisAlignment.start,
          //               children: [
          //                 Row(
          //                   // mainAxisAlignment: MainAxisAlignment.start,
          //                   crossAxisAlignment: CrossAxisAlignment.center,
          //                   mainAxisAlignment: MainAxisAlignment.start,
          //                   children: [
          //                     Container(
          //                       decoration: BoxDecoration(
          //                         color: getColorFromInput(
          //                           (index + 1).toString(),
          //                         ),
          //                         borderRadius: BorderRadius.circular(6),
          //                       ),
          //                       height: 45,
          //                       width: 45,
          //                       child: Center(
          //                         child: Text(
          //                           (index + 1).toString(),
          //                           // "test",
          //                           style: TextStyle(
          //                             color: Colors.white,
          //                             fontSize: 15,
          //                             fontWeight: FontWeight.bold,
          //                           ),
          //                         ),
          //                       ),
          //                     ),
          //                     SizedBox(width: 15),

          //                     Column(
          //                       crossAxisAlignment: CrossAxisAlignment.start,
          //                       children: [
          //                         Text(
          //                           commonTask.title,
          //                           style: TextStyle(
          //                             fontSize: 16,
          //                             fontWeight: FontWeight.w600,
          //                           ),
          //                         ),

          //                         Text(
          //                           truncateText(commonTask.desc, 30),
          //                           style: TextStyle(fontSize: 14),
          //                         ),
          //                       ],
          //                     ),
          //                     Spacer(),
          //                     commonTask.isCompleted
          //                         ? Icon(
          //                           CupertinoIcons.check_mark_circled,
          //                           color: Colors.green,
          //                         )
          //                         : Icon(
          //                           CupertinoIcons.chevron_right,
          //                           size: 20,
          //                         ),
          //                   ],
          //                 ),
          //                 SizedBox(height: 15),
          //                 DetailRowTile(
          //                   title: "Project",
          //                   value: project?.projectTitle ?? "-",
          //                 ),

          //                 SizedBox(height: 5),
          //                 Row(
          //                   children: [
          //                     Text(
          //                       "Due Date",
          //                       style: TextStyle(
          //                         color: AppColors.grey2,
          //                         fontSize: 14,
          //                       ),
          //                     ),
          //                     Spacer(),
          //                     Text(
          //                       commonTask.dueDate.hour == 0 &&
          //                               commonTask.dueDate.minute == 0 &&
          //                               commonTask.dueDate.second == 0
          //                           ? commonTask.dueDate.goodDayDate()
          //                           : commonTask.dueDate.goodDayDateTime(),

          //                       style: TextStyle(
          //                         fontWeight: FontWeight.w600,
          //                         color: getDueDateColor(
          //                           commonTask.dueDate,
          //                           commonTask.isCompleted,
          //                         ),
          //                       ),
          //                     ),
          //                   ],
          //                 ),

          //                 SizedBox(height: 5),

          //                 Row(
          //                   children: [
          //                     Text(
          //                       "Created",
          //                       style: TextStyle(
          //                         color: AppColors.grey2,
          //                         fontSize: 14,
          //                       ),
          //                     ),
          //                     Spacer(),
          //                     Text(
          //                       getTimeAgo(commonTask.createdAt),
          //                       style: TextStyle(fontWeight: FontWeight.w600),
          //                     ),
          //                     SizedBox(width: 5),
          //                     Container(
          //                       padding: EdgeInsets.all(2),
          //                       decoration: BoxDecoration(
          //                         // shape: BoxShape.circle,
          //                         color: AppColors.black,
          //                         // borderRadius: BorderRadius.circular(4),
          //                         shape: BoxShape.circle,
          //                       ),
          //                     ),
          //                     SizedBox(width: 5),

          //                     Text(
          //                       userCubit
          //                               .getUserById(commonTask.createdBy)
          //                               ?.name ??
          //                           "",
          //                       style: TextStyle(fontWeight: FontWeight.w600),
          //                     ),
          //                   ],
          //                 ),

          //                 SizedBox(height: 5),
          //                 DetailRowTile(
          //                   title: "Assigned To",
          //                   value:
          //                       userCubit
          //                           .getUserById(commonTask.assignTo)
          //                           ?.name ??
          //                       "",
          //                 ),
          //                 SizedBox(height: 5),
          //                 commonTask.completedAt != null
          //                     ? DetailRowTile(
          //                       title: "Completed At",
          //                       value:
          //                           commonTask.completedAt?.goodDayDateTime(),
          //                     )
          //                     : SizedBox(),
          //               ],
          //             ),
          //           ),
          //         );
          //       }),
          //       SizedBox(height: 30),
          //     ],
          //   ),
          // );
        }
      },
    );
  }
}
