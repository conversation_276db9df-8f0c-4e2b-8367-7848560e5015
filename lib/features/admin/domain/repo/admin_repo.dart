import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';

abstract class AdminTaskRepo {
  Future<void> createAdminTask(AdminTaskModel adminTask);
  Future<void> updateAdminTask(AdminTaskModel adminTask);
  Future<void> deleteAdminTask(String adminTaskId);
  Stream<List<AdminTaskModel>> getAllAdminTask();
  Future<List<AdminTaskModel>> getCompletedTask(DateTime month);
  Stream<List<AdminTaskModel>> getAdminTaskCreatedByCurrentUser(String userId);
  Future<List<AdminTaskModel>> getCompletedAdminTaskByCurrentUser(
    DateTime month,
    String projectId,
  );
}
