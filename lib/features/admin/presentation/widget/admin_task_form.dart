import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_form_cubit.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class AdminTaskForm extends StatefulWidget {
  AdminTaskModel? editAdminTask;
  AdminTaskForm({super.key, required this.editAdminTask});

  @override
  State<AdminTaskForm> createState() => _AdminTaskFormState();
}

class _AdminTaskFormState extends State<AdminTaskForm> {
  @override
  void initState() {
    context.read<AdminTaskFormCubit>().initializeForm(widget.editAdminTask);
    // context.read<ProjectCubit>().fetchProjects();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AdminTaskFormCubit, AdminTaskFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final adminTaskFormCubit = context.read<AdminTaskFormCubit>();
        return SafeArea(
          child: SingleChildScrollView(
            child: IgnorePointer(
              ignoring: state.isLoading,
              child: Form(
                key: adminTaskFormCubit.formKey,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Text("New Task", style: AppTextStyles.appBarHeading),
                          Spacer(),
                          IconButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            icon: Icon(CupertinoIcons.xmark, size: 20),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 400,
                        mainAxisSpacing: 15,
                        crossAxisSpacing: 30,
                        children: [
                          CustomDropDownField(
                            title: "Project",
                            hintText: "select project",
                            initialValue:
                                adminTaskFormCubit.state.selectedProject,
                            validatorText: "Enter Project",
                            items:
                                context
                                    .watch<ProjectCubit>()
                                    .state
                                    .projects
                                    .map((task) {
                                      return DropdownMenuItem<String>(
                                        value: task.docId,
                                        child: Text(task.projectTitle),
                                      );
                                    })
                                    .toList(),
                            onChanged: (value) {
                              if (value != null) {
                                adminTaskFormCubit.selectProject(value);
                              }
                            },
                            validator: false,
                          ),

                          CustomTextField(
                            controller: adminTaskFormCubit.titleController,
                            hintText: "task name",
                            title: "Task *",
                          ),

                          CustomTextField(
                            controller: adminTaskFormCubit.descController,
                            hintText: "task desc",
                            title: "Description *",
                          ),
                          BlocBuilder<UserCubit, UserState>(
                            builder: (context, state) {
                              return CustomDropDownField(
                                title: "Assign User*",
                                hintText: "Assign to",
                                initialValue:
                                    adminTaskFormCubit.state.selectedUser,
                                validatorText: "Please select a user",
                                items:
                                    state.users.map((user) {
                                      return DropdownMenuItem<String>(
                                        value: user.docId,
                                        child: Text(user.name),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    adminTaskFormCubit.selectAssignUser(value);
                                  }
                                },
                              );
                            },
                          ),

                          true
                              ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("Due Date"),
                                  SizedBox(height: 5),
                                  TextFormField(
                                    readOnly: true,
                                    onTap: () {
                                      adminTaskFormCubit.selectDueDate(context);
                                    },
                                    decoration: InputDecoration(
                                      hintText:
                                          adminTaskFormCubit.state.dueDate !=
                                                  null
                                              ? adminTaskFormCubit.state.dueDate
                                                  ?.goodDayDate()
                                              : "due date",

                                      // state.startDate != null
                                      //     ? state.startDate?.goodDayDate()
                                      //     : "start date",
                                      suffixIcon: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            onPressed: () {
                                              adminTaskFormCubit.selectDueDate(
                                                context,
                                              );
                                              // taskFormCubit.selectStartDate(context);
                                            },
                                            icon: Icon(CupertinoIcons.calendar),
                                          ),
                                          IconButton(
                                            onPressed: () {
                                              adminTaskFormCubit.selectDueTime(
                                                context,
                                              );
                                              // taskFormCubit.selectStartDate(context);
                                            },
                                            icon: Icon(CupertinoIcons.clock),
                                          ),
                                        ],
                                      ),
                                      // Normal border when not focused
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: AppColors.grey2,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),

                                      // Border when focused
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: AppColors.primary,
                                          width: 2,
                                        ), // or any color
                                        borderRadius: BorderRadius.circular(10),
                                      ),

                                      // Border when error is shown
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Colors.red,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),

                                      // Border when focused and error is shown
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Colors.red,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      hintStyle: TextStyle(
                                        color: AppColors.grey2,
                                      ),

                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    initialValue:
                                        adminTaskFormCubit.state.dueDate
                                            ?.goodDayDate() ??
                                        "",
                                    validator: (value) {
                                      if (adminTaskFormCubit.state.dueDate ==
                                          null) {
                                        return "date is required";
                                      }
                                      return null;
                                    },
                                  ),
                                ],
                              )
                              : CustomDateField(
                                hintText:
                                    adminTaskFormCubit.state.dueDate != null
                                        ? adminTaskFormCubit.state.dueDate
                                            ?.goodDayDate()
                                        : "due date",
                                title: "Due Date ",
                                onTap: () {
                                  adminTaskFormCubit.selectDueDate(context);
                                },
                                initialValue:
                                    state.dueDate?.goodDayDate() ?? "",
                              ),

                          PrimaryButton(
                            isLoading: state.isLoading,
                            text: "Save",
                            onPressed: () {
                              adminTaskFormCubit.submit(
                                widget.editAdminTask,
                                context,
                              );
                            },

                            width: 100,
                            height: 36,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
