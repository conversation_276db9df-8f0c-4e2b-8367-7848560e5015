import 'package:cloud_firestore/cloud_firestore.dart';

class ActivityModel {
  final String docId;
  final String projectId;
  final DateTime sendAt;
  final String senderId;
  final String senderName;
  final String? message;
  final String? attachment;
  final String? attachmentType;
  final String? attachmentName;

  ActivityModel({
    required this.docId,
    required this.projectId,
    required this.sendAt,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.attachment,
    required this.attachmentType,
    required this.attachmentName,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      docId: json['docId'] ?? '',
      projectId: json['projectId'] ?? '',
      sendAt: (json['sendAt'] as Timestamp).toDate(),
      senderId: json['senderId'] ?? '',
      senderName: json['senderName'] ?? '',
      message: json['message'] ?? null,
      attachment: json['attachment'] ?? null,
      attachmentType: json['attachmentType'] ?? null,
      attachmentName: json['attachmentName'] ?? null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'sendAt': Timestamp.fromDate(sendAt),
      'senderId': senderId,
      'senderName': senderName,
      'message': message,
      'attachment': attachment,
      'attachmentType': attachmentType,
      'attachmentName': attachmentName,
    };
  }

  Map<String, dynamic> toMap() => toJson();
  factory ActivityModel.fromMap(Map<String, dynamic> map) =>
      ActivityModel.fromJson(map);
  factory ActivityModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return ActivityModel.fromJson({
      ...data,
      'docId': snapshot.id, // override docId from document ID
    });
  }
  ActivityModel copyWith({
    String? docId,
    String? projectId,
    DateTime? sendAt,
    String? senderId,
    String? senderName,
    String? message,
    String? attachment,
    String? attachmentType,

    String? attachmentName,
  }) {
    return ActivityModel(
      docId: docId ?? this.docId,
      projectId: projectId ?? this.projectId,
      sendAt: sendAt ?? this.sendAt,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      attachment: attachment ?? this.attachment,
      attachmentType: attachmentType ?? this.attachmentType,
      attachmentName: attachmentName ?? this.attachmentName,
    );
  }
}
