part of 'activity_cubit.dart';

@immutable
class ActivityState {
  final List<ActivityModel> activities;
  final String message;
  final bool isLoading;
  final bool isLoadingMore;
  final bool hasReachedEnd;

  ActivityState({
    required this.activities,
    required this.message,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasReachedEnd,
  });

  factory ActivityState.initial() {
    return ActivityState(
      activities: [],
      message: '',
      isLoading: false,
      isLoadingMore: false,
      hasReachedEnd: false,
    );
  }

  ActivityState copyWith({
    List<ActivityModel>? activities,
    String? message,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasReachedEnd,
  }) {
    return ActivityState(
      activities: activities ?? this.activities,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasReachedEnd: hasReachedEnd ?? this.hasReachedEnd,
    );
  }
}
