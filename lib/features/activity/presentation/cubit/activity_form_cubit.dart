// ignore_for_file: unused_import

import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/domain/repo/activity_repo.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

part 'activity_form_state.dart';

class ActivityFormCubit extends Cubit<ActivityFormState> {
  final ActivityRepo activityRepo;
  ActivityFormCubit(this.activityRepo) : super(ActivityFormState.initial());

  TextEditingController? messageController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void initializeForm(ActivityModel? editActivity) {
    if (editActivity != null) {
      messageController?.text = editActivity.message ?? '';
      emit(state.copyWith(dbFile: editActivity.attachment));
    } else {
      emit(ActivityFormState.initial());
    }
  }

  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  void submit({
    required BuildContext context,
    required String projectId,
    ActivityModel? editActivity,
  }) async {
    final projectCubit = context.read<ProjectCubit>();
    if (state.isLoading) return;

    if ((messageController?.text.isEmpty ?? true) &&
        state.selectedFile == null) {
      emit(
        state.copyWith(isLoading: false, message: 'Add message or upload file'),
      );
      return;
    }

    emit(state.copyWith(isLoading: true, message: ''));
    final user = context.read<UserCubit>().getUserById(
      FBAuth.auth.currentUser?.uid ?? '',
    );
    final fileUrl =
        state.selectedFile != null
            ? await FirebaseStorageRepo().uploadActivityFile(
              state.selectedFile!,
            )
            : state.dbFile;

    final activity = ActivityModel(
      docId: editActivity?.docId ?? '',
      projectId: projectId,
      sendAt: DateTime.now(),
      senderId: FBAuth.auth.currentUser?.uid ?? '',
      senderName: user?.name ?? '',
      message:
          messageController?.text.isEmpty ?? true
              ? null
              : messageController?.text,
      attachment: fileUrl,
      attachmentType: state.selectedFile?.extension ?? null,
      attachmentName: state.selectedFile?.name ?? '',
    );

    try {
      if (editActivity == null) {
        // Create activity document
        await activityRepo.createActivity(activity);
        messageController?.clear();

        // Create notification

        // get project detail for get users
        final project = projectCubit.fetchProjectById(projectId);

        final notification = NotificationModel(
          docId: '',
          notifyType: NotificationType.activity,
          title: "${project?.projectTitle}",
          message: "${user?.name} : ${activity.message ?? 'new message'}",
          data: {'projectId': projectId},
          topic: projectId.substring(0, 6),
          createdAt: DateTime.now(),
          users: project?.userId ?? [],
        );

        context.read<NotificationFormCubit>().createNotification(notification);

        emit(
          state.copyWith(
            selectedFile: false,
            dbFile: false,
            textmsg: null,
            isLoading: false,
            message: '',
          ),
        );
        //  project last update for green dot
        projectCubit.projectLastUpdateAt(projectId);
        projectCubit.createLastSeen(projectId);

        // close keyboard
        context.read<ActivityCubit>().closeKeyboard(context);
      } else {
        await activityRepo.updateActivity(activity);
        messageController?.clear();
        emit(
          state.copyWith(
            selectedFile: false,
            dbFile: false,
            textmsg: null,
            isLoading: false,
            message: '',
          ),
        );
        //  project last update for green dot
        projectCubit.projectLastUpdateAt(projectId);
        projectCubit.createLastSeen(projectId);
        // close keyboard
        context.read<ActivityCubit>().closeKeyboard(context);
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: 'Failed to send message: ${e.toString()}',
        ),
      );
    }
  }
}
