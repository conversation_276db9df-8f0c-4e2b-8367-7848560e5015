import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActivityForm extends StatefulWidget {
  final String projectId;
  final ActivityModel? editActivity;
  const ActivityForm({super.key, required this.projectId, this.editActivity});

  @override
  State<ActivityForm> createState() => _ActivityFormState();
}

class _ActivityFormState extends State<ActivityForm> {
  @override
  void initState() {
    context.read<ActivityFormCubit>().initializeForm(widget.editActivity);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ActivityFormCubit, ActivityFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final activityFormCubit = context.read<ActivityFormCubit>();
        return IgnorePointer(
          ignoring: state.isLoading,
          child: Form(
            key: context.read<ActivityFormCubit>().formKey,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
              decoration: BoxDecoration(
                color: AppColors.containerGreyColor,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: AppColors.chipGreyColor),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // File preview (if any)
                  if (state.selectedFile != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: buildFilePreview(
                        context: context,
                        selectedFile: state.selectedFile,
                        dbFile: state.dbFile,
                        dbFileExt: widget.editActivity?.attachmentType,
                        dbFileName: widget.editActivity?.attachmentName,
                        isEdit: true,
                        onDelete: () {
                          final isDbFile =
                              state.selectedFile == null &&
                              state.dbFile != null;
                          context.read<ActivityFormCubit>().deletPickFile(
                            isDbFile,
                          );
                        },
                        onView: () {
                          context.read<ActivityFormCubit>().viewPickFile(
                            state.dbFile,
                            context,
                            widget.editActivity?.attachmentType,
                          );
                        },
                        isMessage: false,
                      ),
                    ),
                  if (state.selectedFile != null) SizedBox(height: 10),

                  // Text field and icons
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: activityFormCubit.messageController,
                          decoration: InputDecoration(
                            hintText: "Message",
                            border: InputBorder.none,
                            isDense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                          maxLines: null,
                        ),
                      ),
                      state.isLoading
                          ? Container(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(),
                          )
                          : Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              InkWell(
                                onTap: () {
                                  context.read<ActivityFormCubit>().pickFile(
                                    context,
                                  );
                                },
                                child: Icon(CupertinoIcons.paperclip),
                              ),
                              SizedBox(width: 25),
                              InkWell(
                                onTap: () {
                                  context
                                      .read<ActivityFormCubit>()
                                      .pickFileFromCamera(context);
                                },
                                child: Icon(CupertinoIcons.camera),
                              ),
                              SizedBox(width: 25),
                              TransparentInkWell(
                                onTap: () {
                                  if (!context
                                      .read<ProjectCubit>()
                                      .state
                                      .haveAccess) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          "You are not authorized to perform this action.",
                                        ),
                                      ),
                                    );
                                    return;
                                  }
                                  context.read<ActivityFormCubit>().submit(
                                    context: context,
                                    projectId: widget.projectId,
                                    editActivity: null,
                                  );
                                },
                                child: Icon(Icons.send),
                              ),
                            ],
                          ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
