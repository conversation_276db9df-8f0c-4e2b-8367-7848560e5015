import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/users/domain/repo/user_repo.dart';

class FirebaseUserRepo extends UserRepo {
  final usersRef = FBFireStore.users;
  @override
  Stream<List<UserModel>> getAllUsers() {
    return usersRef.where('deleted', isEqualTo: false).snapshots().map((
      snapshot,
    ) {
      return snapshot.docs
          .map((doc) => UserModel.fromSnap(doc))
          // .where((user) => user.role != 'admin')
          .toList();
    });
  }

  @override
  Future<void> deleteUser(String userId) async {
    await FBFunctions.ff.httpsCallable('deleteUser').call({"userId": userId});
  }

  Future<void> updateProfile(String userId, String name, String role) async {
    await FBFireStore.users.doc(userId).update({'name': name, 'role': role});
  }

  Future<void> forgetPassword(String email) async {
    try {
      print(email);
      await FBAuth.auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception("Failed to send password reset email: $e");
    }
  }
}
