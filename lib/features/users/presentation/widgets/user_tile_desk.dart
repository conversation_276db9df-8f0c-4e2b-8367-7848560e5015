import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/widgets/editprofile_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UserTileDesk extends StatelessWidget {
  const UserTileDesk({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, state) {
        final users =
            state.users.where((user) => user.role != 'admin').toList();
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        }

        return BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
          builder: (context, attendanceState) {
            return SingleChildScrollView(
              child: Column(
                children: [
                  ...List.generate(users.length, (index) {
                    final user = users[index];

                    // Get user's latest punch
                    final userPunches =
                        attendanceState.punches
                            .where((p) => p.userId == user.docId)
                            .toList()
                          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

                    final latestPunch =
                        userPunches.isNotEmpty ? userPunches.first : null;

                    final isPunchedIn = latestPunch?.punchIn ?? false;

                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 30),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              /// Avatar with Punch Status Dot
                              Expanded(
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      backgroundColor: getColorFromInput(
                                        user.name[0],
                                      ),
                                      radius: 18,
                                      child: Text(
                                        user.name[0].toUpperCase(),
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 20,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 15),
                                    Text(
                                      user.name,
                                      style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              Expanded(
                                child: Text(
                                  user.email,
                                  style: TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  user.role,
                                  style: TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Row(
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        showDialog(
                                          useSafeArea: true,
                                          context: context,
                                          builder: (context) {
                                            return BlocProvider(
                                              create:
                                                  (context) => UserFormCubit(
                                                    context
                                                        .read<UserCubit>()
                                                        .userRepo,
                                                  ),
                                              child: Dialog(
                                                child: Container(
                                                  constraints: BoxConstraints(
                                                    maxWidth: 400,
                                                  ),
                                                  child: Padding(
                                                    padding:
                                                        MediaQuery.of(
                                                          context,
                                                        ).viewInsets,
                                                    child: EditProfileForm(
                                                      editUser: user,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                      child: Icon(Icons.edit_rounded, size: 25),
                                    ),
                                    SizedBox(width: 30),
                                    InkWell(
                                      onTap: () {
                                        showUserConfirmDeletDialog(context, () {
                                          context.read<UserCubit>().deleteUser(
                                            user.docId,
                                          );
                                        });
                                      },
                                      child: Icon(
                                        CupertinoIcons.delete,
                                        size: 25,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 20),
                        Divider(color: AppColors.borderGrey),
                        SizedBox(height: 20),
                      ],
                    );
                  }),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

// class UserTileDesk extends StatelessWidget {
//   const UserTileDesk({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<UserCubit, UserState>(
//       builder: (context, state) {
//         if (state.isLoading) {
//           return Center(child: CircularProgressIndicator());
//         }
//         return SingleChildScrollView(
//           child: Column(
//             children: [
//               ...List.generate(state.users.length, (index) {
//                 final user = state.users[index];
//                 return Column(
//                   children: [
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 30),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.start,
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         children: [
//                           Expanded(
//                             child: Row(
//                               children: [
//                                 CircleAvatar(
//                                   backgroundColor: getColorFromInput(
//                                     user.name[0],
//                                   ),
//                                   radius: 18,
//                                   child: Text(
//                                     user.name[0].toUpperCase(),
//                                     style: TextStyle(
//                                       color: Colors.white,
//                                       fontSize: 20,
//                                       fontWeight: FontWeight.w500,
//                                     ),
//                                   ),
//                                 ),
//                                 SizedBox(width: 15),
//                                 Text(
//                                   user.name,
//                                   style: TextStyle(
//                                     fontSize: 15,
//                                     fontWeight: FontWeight.w500,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),

//                           Expanded(
//                             child: Text(
//                               user.email,
//                               style: TextStyle(
//                                 fontSize: 15,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ),

//                           Expanded(
//                             child: Text(
//                               user.role,
//                               style: TextStyle(
//                                 fontSize: 15,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ),
//                           Expanded(
//                             child: Row(
//                               children: [
//                                 InkWell(
//                                   onTap: () {
//                                     showDialog(
//                                       // isScrollControlled: true,
//                                       useSafeArea: true,
//                                       context: context,
//                                       builder: (context) {
//                                         return BlocProvider(
//                                           create:
//                                               (context) => UserFormCubit(
//                                                 context
//                                                     .read<AuthCubit>()
//                                                     .authRepo,
//                                               ),
//                                           child: Dialog(
//                                             child: Container(
//                                               constraints: BoxConstraints(
//                                                 maxWidth: 400,
//                                               ),
//                                               child: Padding(
//                                                 padding:
//                                                     MediaQuery.of(
//                                                       context,
//                                                     ).viewInsets,
//                                                 child: EditProfileForm(
//                                                   editUser: user,
//                                                 ),
//                                               ),
//                                             ),
//                                           ),
//                                         );
//                                       },
//                                     );
//                                   },
//                                   child: Icon(CupertinoIcons.pen, size: 25),
//                                 ),
//                                 SizedBox(width: 30),
//                                 Icon(CupertinoIcons.delete, size: 25),
//                               ],
//                             ),
//                           ),
//                           // Text(user.email),
//                           // Text(user.role),
//                         ],
//                       ),
//                     ),
//                     SizedBox(height: 20),
//                     Divider(color: AppColors.borderGrey),
//                     SizedBox(height: 20),
//                   ],
//                 );
//               }),
//             ],
//           ),
//         );
//       },
//     );
//   }
// }
