import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_form_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChangePasswordForm extends StatelessWidget {
  const ChangePasswordForm({super.key});

  @override
  Widget build(BuildContext context) {
    final userFormCubit = context.read<UserFormCubit>();
    userFormCubit.emailController.text =
        context.read<AuthCubit>().currentUser?.email ?? "";
    return BlocListener<UserFormCubit, UserFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(CupertinoIcons.xmark),
                  ),
                  Text("Change Password", style: AppTextStyles.formtitle),
                  Spacer(),
                ],
              ),
              SizedBox(height: 20),
              CustomTextField(
                controller: userFormCubit.emailController,
                hintText: "enter your email",
                title: "Email",
              ),
              SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: PrimaryButton(
                      isLoading: context.watch<UserFormCubit>().state.isLoading,

                      text: "SEND",
                      onPressed: () {
                        context.read<UserFormCubit>().changePassword(
                          userFormCubit.emailController.text,
                          context,
                        );
                        // Navigator.pop(context);
                      },

                      height: 36,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
