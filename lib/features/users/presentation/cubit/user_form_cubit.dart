import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/users/domain/repo/user_repo.dart';
import 'package:flutter/material.dart';

part 'user_form_state.dart';

class UserFormCubit extends Cubit<UserFormState> {
  UserRepo userRepo;
  UserFormCubit(this.userRepo) : super(UserFormState.initial());

  final emailController = TextEditingController();
  final nameController = TextEditingController();

  void selectRole(String role) {
    emit(state.copyWith(selectedRole: role));
  }

  void changePassword(String email, BuildContext context) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await userRepo.forgetPassword(email);
      emit(
        state.copyWith(isLoading: false, message: "Password reset email sent"),
      );
      Navigator.pop(context);
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to send password reset email: ${e.toString()}",
        ),
      );
      Navigator.pop(context);
    }
  }

  void updateProfile(
    String userId,
    String name,
    String role,
    BuildContext context,
  ) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await userRepo.updateProfile(userId, name, role);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Profile updated successfully",
        ),
      );
      Navigator.pop(context);
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to update profile: ${e.toString()}",
        ),
      );
      Navigator.pop(context);
    }
  }

}
