import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';
import 'package:cp_associates/features/notifications/domain/repo/notification_repo.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cp_associates/core/utils/const.dart';

class FirebaseNotificationRepo implements NotificationRepo {
  final messaging = FirebaseMessaging.instance;

  @override
  Stream<List<NotificationModel>> fetchNotificationBeforeLastSeen(
    DateTime lastSeen,
  ) {
    return FBFireStore.notifications
        .where(
          Filter.and(
            Filter('users', arrayContains: FBAuth.auth.currentUser?.uid ?? ''),
            Filter.or(
              Filter('createdAt', isGreaterThan: lastSeen),
              Filter('data.hasApproved', isEqualTo: false),
            ),
          ),
        )
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => NotificationModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Future<void> updateNotification(String notificationId) async {
    await FBFireStore.notifications.doc(notificationId).update({
      'data.hasApproved': true,
    });
  }

  // Create notification Functions
  @override
  Future<void> createNotification(NotificationModel notification) async {
    await FBFireStore.notifications.add(notification.toJson());
  }

  // @override
  // Future<void> createActivityNotification(
  //   // ActivityNotifiesModel notification,
  //   NotificationModel notification,
  // ) async {
  //   await FBFireStore.notifications.add(notification.toJson());
  // }

  // @override
  // Future<void> createProjectTaskNotification(
  //   NotificationModel notification,
  // ) async {
  //   await FBFireStore.notifications.add(notification.toJson());
  // }

  // @override
  // Future<void> submitProjectTaskNotification(
  //   NotificationModel notification,
  // ) async {
  //   await FBFireStore.notifications.add(notification.toJson());
  // }

  // @override
  // Future<void> attendanceLogNotification(NotificationModel notification) async {
  //   await FBFireStore.notifications.add(notification.toJson());
  // }

  // @override
  // Future<void> requestNotification(NotificationModel notification) async {
  //   await FBFireStore.notifications.add(notification.toJson());
  // }
}
