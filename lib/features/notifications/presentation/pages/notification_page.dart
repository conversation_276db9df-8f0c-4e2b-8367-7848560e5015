import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/task/data/firebase_task_repo.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/task/presentation/pages/task_detail_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_cubit.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/const.dart';

class NotificationPage extends StatelessWidget {
  const NotificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        context.read<NotificationCubit>().setNotificationlastSeen();
        context.read<NotificationCubit>().fetchUnreadNotification();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text("Notifications", style: AppTextStyles.appBarHeading),
        ),
        body: BlocBuilder<NotificationCubit, NotificationState>(
          builder: (context, state) {
            if (state.isLoading) {
              return Center(child: CircularProgressIndicator());
            }
            if (state.notifications.isEmpty) {
              return Center(child: Text("No Notification"));
            }
            if (state.notifications.isNotEmpty) {
              final notifications = state.notifications;

              // Group by type
              final Map<String, List<dynamic>> grouped = {};
              for (var notification in notifications) {
                grouped
                    .putIfAbsent(notification.notifyType, () => [])
                    .add(notification);
              }

              // Sort group types by first created date
              final sortedTypes =
                  grouped.keys.toList()..sort((a, b) {
                    final aDate = grouped[a]!.first.createdAt;
                    final bDate = grouped[b]!.first.createdAt;
                    return aDate.compareTo(bDate);
                  });
              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: sortedTypes.length,
                itemBuilder: (context, index) {
                  final type = sortedTypes[index];
                  final items = grouped[type]!;

                  return SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section Title
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            _getSectionTitle(type),
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: AppColors.black,
                            ),
                          ),
                        ),
                        // Notifications under the section
                        ...items.map((notification) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),

                            child: InkWell(
                              onTap: () {
                                switch (notification.notifyType) {
                                  case NotificationType.activity:
                                    print("Activity Notification");
                                    kIsWeb
                                        ? context.go(
                                          "${Routes.activity}/${notification.data['projectId']}",
                                        )
                                        : context.push(
                                          "${Routes.activity}/${notification.data['projectId']}",
                                        );
                                    break;

                                  case NotificationType.task:
                                    print("Task Notification");
                                    kIsWeb
                                        ? context.go(
                                          "${Routes.taskDetail}/${notification.data['taskId']}",
                                        )
                                        : context.push(
                                          "${Routes.taskDetail}/${notification.data['taskId']}",
                                        );
                                    break;
                                  case NotificationType.attendence:
                                    kIsWeb
                                        ? context.go(Routes.userAttendance)
                                        : context.push(Routes.userAttendance);
                                    break;

                                  case NotificationType.sumbitTask:
                                    kIsWeb
                                        ? showDialog(
                                          context: context,
                                          builder: (context) {
                                            return Dialog(
                                              child: Container(
                                                width: 800,
                                                child: MultiBlocProvider(
                                                  providers: [
                                                    BlocProvider(
                                                      create:
                                                          (context) =>
                                                              TaskCubit(
                                                                  FirebaseTaskRepo(),
                                                                )
                                                                ..fetchTaskById(
                                                                  notification
                                                                      .data['taskId'],
                                                                )
                                                                ..fetchTaskLogs(
                                                                  notification
                                                                      .data['taskId'],
                                                                ),
                                                    ),
                                                    BlocProvider(
                                                      create:
                                                          (
                                                            context,
                                                          ) => TaskFormCubit(
                                                            FirebaseTaskRepo(),
                                                          ),
                                                    ),
                                                  ],
                                                  child: TaskDetailPage(
                                                    taskId:
                                                        notification
                                                            .data['taskId'],
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        )
                                        : context.push(
                                          "${Routes.taskDetail}/${notification.data['taskId']}",
                                        );
                                    break;

                                  case NotificationType.request:
                                    context
                                        .read<AttendanceAdminCubit>()
                                        .approvedReqFromNotification(
                                          notification.data['reqId'],
                                          context,
                                          notification.docId,
                                        );
                                    break;

                                  default:
                                    context
                                        .read<NotificationCubit>()
                                        .handleMessage(
                                          RemoteMessage(
                                            data: {
                                              'type': notification.notifyType,
                                              'projectId':
                                                  notification
                                                      .data['projectId'],
                                              'taskId':
                                                  notification.data['taskId'],
                                            },
                                          ),
                                        );
                                }
                              },

                              child: Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: AppColors.containerGreyColor,
                                  border: Border.all(
                                    color: AppColors.borderGrey,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  children: [
                                    TransparentInkWell(
                                      onTap: () async {
                                        notification.notifyType ==
                                                NotificationType.sumbitTask
                                            ? showApprovedTaskDialog(context, () {
                                              context
                                                  .read<NotificationCubit>()
                                                  .approvedNotificationTaskAndCreateLog(
                                                    notification.data['taskId'],
                                                    notification.docId,
                                                  );
                                            })
                                            : null;
                                      },
                                      child: Icon(
                                        _getIcon(type),
                                        color: AppColors.primary,
                                      ),
                                    ),
                                    const SizedBox(width: 20),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Text(
                                                notification.title,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              Spacer(),
                                              Text(
                                                getTimeAgo(
                                                  notification.createdAt,
                                                ),
                                                style: const TextStyle(
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Text(
                                            (notification.message ?? ""),
                                            style: const TextStyle(
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  );
                },
              );
            }
            return Center(child: Text("No Notification"));
          },
        ),
      ),
    );
  }

  String _getSectionTitle(String type) {
    switch (type) {
      case NotificationType.activity:
        return "Activity";
      case NotificationType.task:
        return "Task";
      case NotificationType.attendence:
        return "Attendance";
      case NotificationType.request:
        return "Request";
      case NotificationType.sumbitTask:
        return "Submit Task";
      default:
        return "Other";
    }
  }

  IconData _getIcon(String type) {
    switch (type) {
      case NotificationType.activity:
        return CupertinoIcons.chat_bubble_text;
      case NotificationType.task:
        return Icons.event;
      case NotificationType.attendence:
        return CupertinoIcons.clock;
      case NotificationType.request:
        return CupertinoIcons.time;
      case NotificationType.sumbitTask:
        return Icons.check_circle_outline;
      default:
        return Icons.notifications;
    }
  }
}
