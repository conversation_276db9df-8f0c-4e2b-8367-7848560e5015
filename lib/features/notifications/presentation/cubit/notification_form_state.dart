part of 'notification_form_cubit.dart';

class NotificationFormState {
  final String message;
  final bool isLoading;

  NotificationFormState({required this.message, required this.isLoading});

  factory NotificationFormState.initial() {
    return NotificationFormState(message: '', isLoading: false);
  }

  NotificationFormState copyWith({String? message, bool? isLoading}) {
    return NotificationFormState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
