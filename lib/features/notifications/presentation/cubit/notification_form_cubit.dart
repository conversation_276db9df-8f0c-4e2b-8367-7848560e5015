import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';
import 'package:cp_associates/features/notifications/domain/repo/notification_repo.dart';

part 'notification_form_state.dart';

class NotificationFormCubit extends Cubit<NotificationFormState> {
  NotificationRepo notificationRepo;
  NotificationFormCubit(this.notificationRepo)
    : super(NotificationFormState.initial());

  void createNotification(NotificationModel notification) {
    emit(state.copyWith(isLoading: true, message: ''));
    notificationRepo.createNotification(notification);
    emit(
      state.copyWith(
        isLoading: false,
        message: 'Notification created successfully',
      ),
    );
  }

  void updateNotification(String notificationId) {
    emit(state.copyWith(isLoading: true, message: ''));
    notificationRepo.updateNotification(notificationId);
    emit(
      state.copyWith(
        isLoading: false,
        message: 'Notification updated successfully',
      ),
    );
  }

  // void createActivityNotification(NotificationModel notification) {
  //   emit(state.copyWith(isLoading: true, message: ''));
  //   notificationRepo.createActivityNotification(notification);
  //   emit(
  //     state.copyWith(
  //       isLoading: false,
  //       message: 'Notification created successfully',
  //     ),
  //   );
  // }

  // void createProjectTaskNotification(NotificationModel notification) {
  //   emit(state.copyWith(isLoading: true, message: ''));
  //   notificationRepo.createProjectTaskNotification(notification);
  //   emit(
  //     state.copyWith(
  //       isLoading: false,
  //       message: 'Notification created successfully',
  //     ),
  //   );
  // }

  // void submitProjectTaskNotification(NotificationModel notification) {
  //   emit(state.copyWith(isLoading: true, message: ''));
  //   notificationRepo.submitProjectTaskNotification(notification);
  //   emit(
  //     state.copyWith(
  //       isLoading: false,
  //       message: 'Notification created successfully',
  //     ),
  //   );
  // }

  // void attendanceLogNotification(NotificationModel notification) {
  //   emit(state.copyWith(isLoading: true, message: ''));
  //   notificationRepo.attendanceLogNotification(notification);
  //   emit(
  //     state.copyWith(
  //       isLoading: false,
  //       message: 'Notification created successfully',
  //     ),
  //   );
  // }

  // void requestNotification(NotificationModel notification) {
  //   emit(state.copyWith(isLoading: true, message: ''));
  //   notificationRepo.requestNotification(notification);
  //   emit(
  //     state.copyWith(
  //       isLoading: false,
  //       message: 'Notification created successfully',
  //     ),
  //   );
  // }
}
