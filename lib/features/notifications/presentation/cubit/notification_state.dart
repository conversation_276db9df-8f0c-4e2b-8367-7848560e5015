part of 'notification_cubit.dart';

class NotificationState {
  final List<NotificationModel> notifications;
  final bool isPermissionGranted;
  final bool hasUnreadNotification;
  final String message;
  final bool isLoading;

  NotificationState({
    required this.notifications,
    required this.isPermissionGranted,
    required this.hasUnreadNotification,
    required this.message,
    required this.isLoading,
  });

  factory NotificationState.initial() {
    return NotificationState(
      isPermissionGranted: false,
      hasUnreadNotification: false,
      notifications: [],
      message: '',
      isLoading: false,
    );
  }

  NotificationState copyWith({
    bool? isPermissionGranted,
    List<NotificationModel>? notifications,
    bool? hasUnreadNotification,
    String? message,
    bool? isLoading,
  }) {
    return NotificationState(
      isPermissionGranted: isPermissionGranted ?? this.isPermissionGranted,
      hasUnreadNotification:
          hasUnreadNotification ?? this.hasUnreadNotification,
      notifications: notifications ?? this.notifications,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
