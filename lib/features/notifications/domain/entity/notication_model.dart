import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationModel {
  final String docId;
  final String notifyType;
  final String title;
  final String message;
  final Map<String, dynamic>? data;
  final String? token;
  final String? topic;
  final DateTime createdAt;
  final List<String> users;

  NotificationModel({
    required this.docId,
    required this.notifyType,
    required this.title,
    required this.message,
    this.data,
    this.token,
    this.topic,
    required this.createdAt,
    required this.users,
  });
  NotificationModel copyWith({
    String? docId,
    String? notifyType,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    String? token,
    String? topic,
    DateTime? createdAt,
    List<String>? users,
  }) {
    return NotificationModel(
      docId: docId ?? this.docId,
      notifyType: notifyType ?? this.notifyType,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      token: token ?? this.token,
      topic: topic ?? this.topic,
      createdAt: createdAt ?? this.createdAt,
      users: users ?? this.users,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'notifyType': notifyType,
      'title': title,
      'message': message,
      'data': data,

      'token': token,
      'topic': topic,
      'createdAt': createdAt,
      'users': users,
    };
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      docId: json['docId'] ?? '',
      notifyType: json['notifyType'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      data: json['data'] ?? null,
      token: json['token'] ?? null,
      topic: json['topic'] ?? null,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      users: List<String>.from(json['users'] ?? []),
    );
  }
  factory NotificationModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return NotificationModel.fromJson({
      ...data,
      'docId': snapshot.id, // override docId from document ID
    });
  }
  Map<String, dynamic> toJson() {
    return {
      'notifyType': notifyType,
      'title': title,
      'message': message,
      'data': data,
      'token': token,
      'topic': topic,
      'createdAt': createdAt,
      'users': users,
    };
  }
}
