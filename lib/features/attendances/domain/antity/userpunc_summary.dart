import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';

class UserPunchSummary {
  final UserModel user;
  final PunchesModel? latestPunch;
  final List<PunchesModel> userPunches;

  UserPunchSummary({
    required this.user,
    required this.latestPunch,
    required this.userPunches,
  });

  bool get isPunchedIn => latestPunch?.punchIn ?? false;

  DateTime? get latestPunchTime => latestPunch?.createdAt;
}
