import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/attendances/presentation/widget/request_tile.dart';
import 'package:flutter/material.dart';

class RequestPage extends StatelessWidget {
  const RequestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveWidCustom(
      mobile: Scaffold(
        appBar: AppBar(title: const Text("Requests")),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: RequestTile(),
        ),
      ),
      desktop: Scaffold(
        appBar: AppBar(title: const Text("test")),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: RequestTile(),
        ),
      ),
    );
  }
}
