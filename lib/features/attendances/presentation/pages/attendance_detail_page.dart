import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_cubit.dart';
import 'package:cp_associates/features/home/<USER>/cubit/setting_cubit.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

class AttendanceDetailPage extends StatefulWidget {
  final String userId;
  const AttendanceDetailPage({super.key, required this.userId});

  @override
  State<AttendanceDetailPage> createState() => _AttendanceDetailPageState();
}

class _AttendanceDetailPageState extends State<AttendanceDetailPage> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttendanceCubit, AttendanceState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Scaffold(body: Center(child: CircularProgressIndicator()));
        }
        final cubit = context.read<AttendanceCubit>();

        double calculateTotalWorkedMinutes(List<PunchesModel> punches) {
          punches.sort((a, b) => a.createdAt.compareTo(b.createdAt));

          double totalMinutes = 0.0;

          for (int i = 0; i < punches.length - 1; i++) {
            final current = punches[i];
            final next = punches[i + 1];

            if (current.punchIn && !next.punchIn) {
              final duration = next.createdAt.difference(current.createdAt);
              totalMinutes += duration.inMinutes;
              i++;
            }
          }

          return totalMinutes;
        }

        final totalMinutes = calculateTotalWorkedMinutes(state.filteredPunches);
        final hours = totalMinutes ~/ 60;
        final minutes = (totalMinutes % 60).toStringAsFixed(0);
        return ResponsiveWid(
          mobile: Scaffold(
            appBar: AppBar(
              title: Text(
                "Attendance Details",
                style: AppTextStyles.appBarHeading,
              ),
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // FILTER TOGGLES
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        filterRadioWidget(
                          label: AttendanceFilter.today,
                          groupValue: state.selectedFilter,
                          context: context,
                        ),
                        const SizedBox(width: 10),
                        filterRadioWidget(
                          label: AttendanceFilter.month,
                          groupValue: state.selectedFilter,
                          context: context,
                        ),
                        const SizedBox(width: 10),
                        InkWell(
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(2023),
                              lastDate: DateTime(2100),
                            );
                            if (date != null) {
                              cubit.selectedFilter(
                                widget.userId,
                                AttendanceFilter.custom,
                                customDate: date,
                              );
                            }
                          },
                          child: Icon(
                            CupertinoIcons.calendar,
                            size: 30,
                            color:
                                state.selectedFilter == AttendanceFilter.custom
                                    ? AppColors.primary
                                    : AppColors.black,
                          ),
                        ),
                        // filterRadioWidget(
                        //   label: AttendanceFilter.custom,
                        //   groupValue: state.selectedFilter,
                        //   context: context,
                        //   onTap: () async {
                        //     final date = await showDatePicker(
                        //       context: context,
                        //       initialDate: DateTime.now(),
                        //       firstDate: DateTime(2023),
                        //       lastDate: DateTime(2100),
                        //     );
                        //     if (date != null) {
                        //       cubit.selectedFilter(
                        //         widget.userId,
                        //         AttendanceFilter.custom,
                        //         customDate: date,
                        //       );
                        //     }
                        //   },
                        // ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  if (state.selectedFilter == AttendanceFilter.month)
                    monthlySummaryWidget(state)
                  else
                    dailySummaryWidget(
                      state,
                      hours,
                      int.tryParse(minutes) ?? 0,
                    ),
                ],
              ),
            ),
          ),

          desktop: Scaffold(
            backgroundColor: AppColors.containerGreyColor,
            appBar: PreferredSize(
              child: CommonAppBar(),
              preferredSize: Size.fromHeight(60),
            ),
            body: GestureDetector(
              onTap: () {
                final settingCubit = context.read<SettingCubit>();
                if (settingCubit.controller.isShowing) {
                  settingCubit.controller.toggle();
                } else {
                  null;
                }
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 160,
                  vertical: 50,
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        InkWell(
                          onTap: () {
                            context.go(Routes.home);
                          },
                          child: Icon(Icons.arrow_back),
                        ),
                        SizedBox(width: 30),
                        Text(
                          "Attendance Details",
                          style: AppTextStyles.appBarHeading,
                        ),
                      ],
                    ),
                    SizedBox(height: 30),
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 30,
                        horizontal: 10,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        // border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 15,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: [
                                  filterRadioWidget(
                                    label: AttendanceFilter.today,
                                    groupValue: state.selectedFilter,
                                    context: context,
                                  ),
                                  const SizedBox(width: 10),
                                  filterRadioWidget(
                                    label: AttendanceFilter.month,
                                    groupValue: state.selectedFilter,
                                    context: context,
                                  ),
                                  const SizedBox(width: 10),
                                  InkWell(
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: DateTime.now(),
                                        firstDate: DateTime(2023),
                                        lastDate: DateTime(2100),
                                      );
                                      if (date != null) {
                                        cubit.selectedFilter(
                                          widget.userId,
                                          AttendanceFilter.custom,
                                          customDate: date,
                                        );
                                      }
                                    },
                                    child: Icon(
                                      CupertinoIcons.calendar,
                                      size: 30,
                                      color:
                                          state.selectedFilter ==
                                                  AttendanceFilter.custom
                                              ? AppColors.primary
                                              : AppColors.black,
                                    ),
                                  ),
                                  // filterRadioWidget(
                                  //   label: AttendanceFilter.custom,
                                  //   groupValue: state.selectedFilter,
                                  //   context: context,
                                  //   onTap: () async {
                                  //     final date = await showDatePicker(
                                  //       context: context,
                                  //       initialDate: DateTime.now(),
                                  //       firstDate: DateTime(2023),
                                  //       lastDate: DateTime(2100),
                                  //     );
                                  //     if (date != null) {
                                  //       cubit.selectedFilter(
                                  //         widget.userId,
                                  //         AttendanceFilter.custom,
                                  //         customDate: date,
                                  //       );
                                  //     }
                                  //   },
                                  // ),
                                ],
                              ),
                            ),
                          ),
                          Divider(),

                          if (state.selectedFilter == AttendanceFilter.month)
                            Container(
                              width: 500,
                              height: 200,
                              child: monthlySummaryWidget(state),
                            )
                          else
                            Container(
                              width: 500,
                              child: dailySummaryWidget(
                                state,
                                hours,
                                int.tryParse(minutes) ?? 0,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget filterRadioWidget({
    required String label,
    required String groupValue,
    required BuildContext context,
    VoidCallback? onTap,
  }) {
    final isSelected = label == groupValue;
    return GestureDetector(
      onTap:
          onTap ??
          () {
            context.read<AttendanceCubit>().selectedFilter(
              widget.userId,
              label,
            );
          },
      child: Container(
        height: 40,
        padding: const EdgeInsets.only(right: 20),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.secondary : Colors.transparent,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Radio<String>(
              activeColor: AppColors.white,
              value: label,
              groupValue: groupValue,
              onChanged: (_) {
                if (onTap != null) {
                  onTap();
                } else {
                  context.read<AttendanceCubit>().selectedFilter(
                    widget.userId,
                    label,
                  );
                }
              },
            ),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.white : Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Daily/custom punch summary
  Widget dailySummaryWidget(AttendanceState state, int hours, int minutes) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCard(
            icon: CupertinoIcons.clock,
            title: "Total Hours",
            value: "$hours h & $minutes min",
          ),
          const SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              color: AppColors.containerGreyColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Attendance Summary",
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: const [
                      Expanded(child: Text("Sr.No")),
                      Expanded(child: Text("Punch")),
                      Expanded(child: Text("Time")),
                    ],
                  ),
                  const Divider(),

                  state.filteredPunches.isEmpty
                      ? Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: const Center(child: Text("No Punch Avaliable")),
                      )
                      : const SizedBox(),
                  ...List.generate(state.filteredPunches.length, (index) {
                    final punch = state.filteredPunches[index];

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          Expanded(child: Text("${index + 1}")),
                          Expanded(
                            child: Text(
                              punch.punchIn ? "Punch In" : "Punch Out",
                            ),
                          ),
                          Expanded(child: Text(punch.createdAt.goodTime())),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Monthly summary (from RecordsModel)
  Widget monthlySummaryWidget(AttendanceState state) {
    final record = state.currentRecord;
    final totalMinutes = record?.totalMinutes ?? 0;
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    final attendanceDays = record?.totalAttendance ?? 0;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 10),
          child: Row(
            children: [
              Expanded(
                child: DropdownButtonHideUnderline(
                  child: DropdownButtonFormField<int>(
                    decoration: InputDecoration(
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: AppColors.grey2),
                      ),
                      hintText: "Select Month",
                      hintStyle: AppTextStyles.hintText,

                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    value: state.selectedMonth,
                    onChanged: (value) {
                      if (value != null) {
                        // print(value);
                        context.read<AttendanceCubit>().selectMonth(value);
                        context.read<AttendanceCubit>().fetchUserMonthlyRecord(
                          widget.userId,
                          DateTime(DateTime.now().year, value),
                        );
                      }
                    },
                    items:
                        true
                            ? dropdownItems
                            : List.generate(12, (index) {
                              final monthIndex = index + 1;

                              return DropdownMenuItem(
                                value: monthIndex,
                                child: Text(monthMap[monthIndex] ?? ""),
                              );
                            }),
                  ),
                ),
              ),
            ],
          ),
        ),
        // Summary Container
        Container(
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
            color: AppColors.containerGreyColor,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(CupertinoIcons.clock),
                  SizedBox(width: 10),
                  Text("Monthly Summary"),
                ],
              ),
              const SizedBox(height: 15),
              Text(
                "Total Hours: $hours h & $minutes min",
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 10),
              Text(
                "Total Attendance: $attendanceDays days",
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Reusable summary card widget
  Widget _buildSummaryCard({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
      decoration: BoxDecoration(
        color: AppColors.containerGreyColor,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(children: [Icon(icon), const SizedBox(width: 10), Text(title)]),
          const SizedBox(height: 10),
          Padding(padding: const EdgeInsets.only(left: 35), child: Text(value)),
        ],
      ),
    );
  }
}
