import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/attendances/data/firebase_attendance_repo.dart';
import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/records_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/request_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/userpunc_summary.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'attendance_admin_state.dart';

class AttendanceAdminCubit extends Cubit<AttendanceAdminState> {
  FirebaseAttendanceRepo attendanceRepo;
  AttendanceAdminCubit(this.attendanceRepo)
    : super(AttendanceAdminState.initial());

  StreamSubscription<List<RequestModel>>? requestsStream;
  StreamSubscription<List<PunchesModel>>? punchesStream;
  StreamSubscription<RecordsModel>? recordsStream;

  // FETCH METHODS
  void fetchAllUserToadyPunchesRecord() {
    emit(state.copyWith(isLoading: true, message: ""));

    punchesStream?.cancel();

    punchesStream = attendanceRepo.fetchAllUserToadyPunchesRecord().listen(
      (punches) {
        print("AllUserTodayPunchesStream-${punches.length}");
        emit(state.copyWith(punches: punches, isLoading: false, message: ""));
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch punches: ${error.toString()}",
          ),
        );
      },
    );
  }

  void fetchActiveRequests() {
    emit(state.copyWith(isLoading: true, message: ""));

    requestsStream?.cancel();

    requestsStream = attendanceRepo.fetchActiveRequests().listen(
      (requests) {
        print("RequestsStream-${requests.length}");
        emit(state.copyWith(requests: requests, isLoading: false, message: ""));
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch requests: ${error.toString()}",
          ),
        );
      },
    );
  }

  void fetchUserMonthlyRecords(String userId, DateTime month) {
    print("RecordsStream------");
    emit(state.copyWith(isLoading: true, message: ""));

    recordsStream?.cancel();

    recordsStream = attendanceRepo
        .fetchUserMonthlyRecords(userId, month)
        .listen(
          (record) {
            print("RecordsStream------2");
            emit(
              state.copyWith(records: [record], isLoading: false, message: ""),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch records: ${error.toString()}",
              ),
            );
          },
        );
  }

  //APPROVE REQUEST
  Future<DateTime?> selectDateTime(
    BuildContext context,
    DateTime initialDateTime,
  ) async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDateTime,
      firstDate: DateTime(2022),
      lastDate: DateTime(2100),
    );

    if (pickedDate == null) return null;

    final pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(initialDateTime),
    );

    if (pickedTime == null) return null;

    return DateTime(
      pickedDate.year,
      pickedDate.month,
      pickedDate.day,
      pickedTime.hour,
      pickedTime.minute,
    );
  }

  void approveRequest(RequestModel request, DateTime updatedTime) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await attendanceRepo.approveRequestAndPunchOut(request, updatedTime);
      emit(state.copyWith(isLoading: false, message: "Request approved"));
    } catch (e) {
      emit(
        state.copyWith(isLoading: false, message: "Failed to approve request"),
      );
    }
  }

  void approvedReqFromNotification(
    String reqId,
    BuildContext context,
    String notificationId,
  ) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      final request = state.requests.firstWhere((req) => req.docId == reqId);
      final selectedDateTime = await selectDateTime(context, request.reqTime);
      if (selectedDateTime != null) {
        await attendanceRepo.approveRequestAndPunchOut(
          request,
          selectedDateTime,
        );
      }
      emit(state.copyWith(isLoading: false, message: "Request approved"));
      context.read<NotificationCubit>().markNotificationAsRead(notificationId);
      ;
    } catch (e) {
      emit(
        state.copyWith(isLoading: false, message: "Failed to approve request"),
      );
    }
  }

  // SORTING METHODS
  List<UserPunchSummary> getSortedUserPunchSummaries(List<UserModel> users) {
    final punches = state.punches;

    final summaries =
        users.map((user) {
          final userPunches =
              punches.where((p) => p.userId == user.docId).toList()..sort(
                (a, b) => b.createdAt.compareTo(a.createdAt),
              ); // newest first

          final latestPunch = userPunches.isNotEmpty ? userPunches.first : null;

          return UserPunchSummary(
            user: user,
            latestPunch: latestPunch,
            userPunches: userPunches,
          );
        }).toList();

    // Sort: punched in users first, then by punch-in time ascending
    summaries.sort((a, b) {
      if (a.isPunchedIn && !b.isPunchedIn) return -1;
      if (!a.isPunchedIn && b.isPunchedIn) return 1;

      if (a.isPunchedIn && b.isPunchedIn) {
        return a.latestPunchTime!.compareTo(b.latestPunchTime!);
      }

      return 0; // both not punched in
    });

    return summaries;
  }

  //UI METHODS

  @override
  Future<void> close() {
    requestsStream?.cancel();
    punchesStream?.cancel();
    recordsStream?.cancel();
    return super.close();
  }
}
