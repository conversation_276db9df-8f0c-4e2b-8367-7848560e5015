import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class AttendanceDeskTile extends StatelessWidget {
  const AttendanceDeskTile({super.key});

  @override
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, userState) {
        final users =
            userState.users.where((user) => user.role != 'admin').toList();
        if (userState.isLoading) {
          return Center(child: CircularProgressIndicator());
        }

        return BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
          builder: (context, attendanceState) {
            final sortedUsers = context
                .read<AttendanceAdminCubit>()
                .getSortedUserPunchSummaries(users);

            return SingleChildScrollView(
              child: Column(
                children: [
                  ...List.generate(sortedUsers.length, (index) {
                    final summary = sortedUsers[index];
                    final user = summary.user;
                    final userPunches = summary.userPunches;
                    final isPunchedIn = summary.isPunchedIn;

                    return TransparentInkWell(
                      onTap: () {
                        context.go("${Routes.attendanceDetail}/${user.docId}");
                      },
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 200,
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        margin: EdgeInsets.only(right: 10),
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color:
                                              isPunchedIn
                                                  ? Colors.green
                                                  : Colors.red,
                                        ),
                                      ),
                                      CircleAvatar(
                                        backgroundColor: getColorFromInput(
                                          user.name[0],
                                        ),
                                        radius: 18,
                                        child: Text(
                                          user.name[0].toUpperCase(),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 20,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 15),
                                      Text(
                                        user.name,
                                        style: TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 100),
                                ...userPunches.reversed.map(
                                  (userPunch) => SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                        right: 100,
                                      ),
                                      child: Text(
                                        userPunch.createdAt.goodTime(),
                                        style: TextStyle(
                                          color:
                                              userPunch.punchIn
                                                  ? Colors.green
                                                  : Colors.red,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 20),
                          sortedUsers.length - 1 == index
                              ? SizedBox()
                              : Divider(color: AppColors.borderGrey),
                          SizedBox(height: 20),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
