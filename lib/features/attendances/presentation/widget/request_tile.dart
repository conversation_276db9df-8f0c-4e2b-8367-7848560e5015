import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequestTile extends StatelessWidget {
  const RequestTile({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
      builder: (context, state) {
        if (state.requests.isEmpty) {
          return Center(child: Text("No Request Avaliable"));
        }
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          child: Column(
            children: [
              ...List.generate(state.requests.length, (index) {
                final request = state.requests[index];
                final user = context.read<UserCubit>().getUserById(request.uId);

                final statusText = request.active ? "Active" : "Approved";
                final statusColor =
                    request.active ? Colors.orange : Colors.green;

                return TransparentInkWell(
                  onTap: () async {
                    final selectedDateTime = await context
                        .read<AttendanceAdminCubit>()
                        .selectDateTime(context, request.reqTime);
                    if (selectedDateTime != null) {
                      context.read<AttendanceAdminCubit>().approveRequest(
                        request,
                        selectedDateTime,
                      );
                    }
                  },

                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppColors.containerGreyColor,
                      border: Border.all(color: AppColors.borderGrey),
                      borderRadius: BorderRadius.circular(12),
                    ),

                    child: Row(
                      children: [
                        Container(
                          height: 60,
                          width: 60,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: getColorFromInput(user?.name ?? "?"),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Center(
                            child: Text(
                              (user?.name ?? "?")[0].toUpperCase(),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 20),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("${user?.name ?? ""}  (${user?.role ?? ""})"),
                            // Text(user?.email ?? ""),
                            Text(
                              "Requested At: ${request.createdAt.goodTime()}",
                            ),
                            Text(
                              "Requested Time: ${request.reqTime.goodTime()}",
                            ),
                          ],
                        ),
                        Spacer(),
                        Icon(
                          CupertinoIcons.check_mark_circled,
                          size: 25,
                          color: AppColors.primary,
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
