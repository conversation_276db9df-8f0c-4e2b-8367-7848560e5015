import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/widgets/editprofile_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class AttendanceMobTile extends StatelessWidget {
  const AttendanceMobTile({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, userState) {
        final users =
            userState.users.where((user) => user.role != 'admin').toList();
        if (userState.isLoading) {
          return Center(child: CircularProgressIndicator());
        }

        return BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
          builder: (context, attendanceState) {
            final sortedSummaries = context
                .read<AttendanceAdminCubit>()
                .getSortedUserPunchSummaries(users);

            return SingleChildScrollView(
              child: Column(
                children: [
                  ...List.generate(sortedSummaries.length, (index) {
                    final summary = sortedSummaries[index];
                    final user = summary.user;
                    final latestPunch =
                        summary.userPunches.isNotEmpty
                            ? summary.userPunches.last
                            : null;

                    final punchStatus =
                        latestPunch == null
                            ? "No Record"
                            : latestPunch.punchIn
                            ? "Punched In"
                            : "Punched Out";

                    final punchTime =
                        latestPunch == null
                            ? ""
                            : latestPunch.createdAt.goodTime();

                    return InkWell(
                      onDoubleTap: () {
                        showModalBottomSheet(
                          isScrollControlled: true,
                          useSafeArea: true,
                          context: context,
                          builder: (context) {
                            return BlocProvider(
                              create:
                                  (context) => UserFormCubit(
                                    context.read<UserCubit>().userRepo,
                                  ),
                              child: Padding(
                                padding: MediaQuery.of(context).viewInsets,
                                child: EditProfileForm(editUser: user),
                              ),
                            );
                          },
                        );
                      },
                      onTap: () {
                        context.push(
                          "${Routes.attendanceDetail}/${user.docId}",
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 15),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 18,
                            horizontal: 10,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.borderGrey),
                            borderRadius: BorderRadius.circular(10),
                            color: AppColors.containerGreyColor,
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    width: 2,
                                    color:
                                        punchStatus == "Punched In"
                                            ? const Color.fromARGB(
                                              255,
                                              0,
                                              240,
                                              8,
                                            )
                                            : Colors.red,
                                  ),
                                ),
                                child: CircleAvatar(
                                  backgroundColor: getColorFromInput(
                                    user.name[0],
                                  ),
                                  radius: 20,
                                  child: Text(
                                    user.name[0].toUpperCase(),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 10),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("${user.name} (${user.role})"),
                                  SizedBox(height: 5),
                                  Text(
                                    "Punched In: ${punchTime.isEmpty ? "No Record" : punchTime}",
                                  ),
                                ],
                              ),
                              Spacer(),
                              Icon(CupertinoIcons.right_chevron, size: 15),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
