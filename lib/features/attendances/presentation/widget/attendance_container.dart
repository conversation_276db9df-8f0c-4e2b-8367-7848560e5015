import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/timer.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class AttendanceContainer extends StatefulWidget {
  final bool isMobile;
  const AttendanceContainer({super.key, required this.isMobile});

  @override
  State<AttendanceContainer> createState() => _AttendanceContainerState();
}

class _AttendanceContainerState extends State<AttendanceContainer> {
  @override
  void initState() {
    final userId = FBAuth.auth.currentUser?.uid ?? '';
    final attendanceCubit = context.read<AttendanceCubit>();
    attendanceCubit.listenToRequestStatus(userId);

    attendanceCubit.listenToLastPunch(userId);

    attendanceCubit.showReqPunchOut(userId);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final attendanceCubit = context.read<AttendanceCubit>();
    return BlocConsumer<AttendanceCubit, AttendanceState>(
      listener: (context, state) {
        if (!state.hasActiveRequest && state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },

      builder: (context, state) {
        // final bool after7pm = DateTime.now().isAfter(reqPunchOutTime);

        return TransparentInkWell(
          onTap: () {
            context.read<AuthCubit>().currentUser?.role == "admin"
                ? kIsWeb
                    ? context.go(Routes.userAttendance)
                    : context.push(Routes.userAttendance)
                : kIsWeb
                ? context.go(
                  "${Routes.attendanceDetail}/${FBAuth.auth.currentUser?.uid}",
                )
                : context.push(
                  "${Routes.attendanceDetail}/${FBAuth.auth.currentUser?.uid}",
                );
          },
          child: Container(
            padding: EdgeInsets.all(widget.isMobile ? 20 : 40),
            height: widget.isMobile ? null : 240,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.primary, AppColors.secondary],
              ),
              borderRadius: BorderRadius.circular(13),
            ),
            child:
                widget.isMobile
                    ? Column(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            buildAttendanceHeader(context, true),
                            const SizedBox(height: 10),
                            Row(
                              children: [
                                context
                                            .read<AuthCubit>()
                                            .state
                                            .currentUser
                                            ?.role ==
                                        "admin"
                                    ? SizedBox()
                                    : Expanded(
                                      child:
                                          state.hasActiveRequest
                                              ? attendanceBtn(
                                                "REQUEST PENDING",
                                                () {},
                                                true,
                                                state,
                                              )
                                              : state.isPunchedIn
                                              ? state.showRequestPunchOut
                                                  ? attendanceBtn(
                                                    "REQ PUNCH OUT",
                                                    () async {
                                                      final selectedDateTime =
                                                          await attendanceCubit
                                                              .selectReqPunchOutDateTime(
                                                                context,
                                                                DateTime.now(),
                                                              );
                                                      if (selectedDateTime !=
                                                          null) {
                                                        attendanceCubit
                                                            .requestPuchOutAfter7pm(
                                                              selectedDateTime,
                                                              context,
                                                            );
                                                      }
                                                    },
                                                    false,
                                                    state,
                                                  )
                                                  : attendanceBtn(
                                                    "PUNCH OUT",
                                                    () => attendanceCubit
                                                        .punchOut(
                                                          DateTime.now(),
                                                          context,
                                                        ),

                                                    false,
                                                    state,
                                                  )
                                              : attendanceBtn(
                                                "PUNCH IN",
                                                () => attendanceCubit.punchIn(
                                                  context,
                                                ),
                                                false,
                                                state,
                                              ),
                                    ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    )
                    : Stack(
                      children: [
                        //Top Right Arrow
                        Align(
                          alignment: Alignment.topRight,
                          child: InkWell(
                            onTap:
                                () =>
                                    context
                                                .read<AuthCubit>()
                                                .currentUser
                                                ?.role ==
                                            "admin"
                                        ? kIsWeb
                                            ? context.go(Routes.userAttendance)
                                            : context.push(
                                              Routes.userAttendance,
                                            )
                                        : kIsWeb
                                        ? context.go(
                                          "${Routes.attendanceDetail}/${FBAuth.auth.currentUser?.uid}",
                                        )
                                        : context.push(
                                          "${Routes.attendanceDetail}/${FBAuth.auth.currentUser?.uid}",
                                        ),

                            child: const Icon(
                              CupertinoIcons.chevron_right,
                              color: AppColors.white,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            // AttendHeader
                            Expanded(
                              flex: 2,
                              child: buildAttendanceHeader(context, false),
                            ),
                            const SizedBox(width: 10),
                            // Punchin btn
                            if (context
                                    .read<AuthCubit>()
                                    .state
                                    .currentUser
                                    ?.role !=
                                "admin")
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Row(
                                      children: [
                                        context
                                                    .read<AuthCubit>()
                                                    .state
                                                    .currentUser
                                                    ?.role ==
                                                "admin"
                                            ? SizedBox()
                                            : Expanded(
                                              child:
                                                  state.hasActiveRequest
                                                      ? attendanceBtn(
                                                        "REQUEST PENDING",
                                                        () {},
                                                        true,
                                                        state,
                                                      )
                                                      : state.isPunchedIn
                                                      ? state.showRequestPunchOut
                                                          ? attendanceBtn(
                                                            "REQ PUNCH OUT",
                                                            () async {
                                                              final selectedDateTime =
                                                                  await attendanceCubit
                                                                      .selectReqPunchOutDateTime(
                                                                        context,
                                                                        DateTime.now(),
                                                                      );
                                                              if (selectedDateTime !=
                                                                  null) {
                                                                attendanceCubit
                                                                    .requestPuchOutAfter7pm(
                                                                      selectedDateTime,
                                                                      context,
                                                                    );
                                                              }
                                                            },
                                                            false,
                                                            state,
                                                          )
                                                          : attendanceBtn(
                                                            "PUNCH OUT",
                                                            () => attendanceCubit
                                                                .punchOut(
                                                                  DateTime.now(),
                                                                  context,
                                                                ),
                                                            false,
                                                            state,
                                                          )
                                                      : attendanceBtn(
                                                        "PUNCH IN",
                                                        () => attendanceCubit
                                                            .punchIn(context),
                                                        false,
                                                        state,
                                                      ),
                                            ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
          ),
        );
      },
    );
  }

  Widget buildAttendanceHeader(BuildContext context, bool isMobile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              "Attendance",
              style: TextStyle(color: AppColors.white, fontSize: 16),
            ),
            const Spacer(),
            if (isMobile)
              InkWell(
                onTap:
                    () =>
                        context.read<AuthCubit>().currentUser?.role == "admin"
                            ? kIsWeb
                                ? context.go(Routes.userAttendance)
                                : context.push(Routes.userAttendance)
                            : kIsWeb
                            ? context.go(
                              "${Routes.attendanceDetail}/${FBAuth.auth.currentUser?.uid}",
                            )
                            : context.push(
                              "${Routes.attendanceDetail}/${FBAuth.auth.currentUser?.uid}",
                            ),

                child: const Icon(
                  CupertinoIcons.chevron_right,
                  color: AppColors.white,
                ),
              ),
          ],
        ),
        Text(
          DateTime.now().goodDayDate(),
          style: TextStyle(
            color: AppColors.white,
            fontSize: widget.isMobile ? 26 : 46,
          ),
        ),
        const SizedBox(height: 10),
        TimeDisplayWidget(),
      ],
    );
  }

  Widget attendanceBtn(
    String text,
    VoidCallback onClick,
    bool disabled,
    AttendanceState state,
  ) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding:
            widget.isMobile ? null : const EdgeInsets.symmetric(vertical: 20),
        backgroundColor: disabled ? Colors.transparent : AppColors.black,
        foregroundColor: AppColors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: AppTextStyles.button,
      ),
      onPressed: disabled ? null : onClick,
      child:
          state.isLoading
              ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(color: Colors.white),
              )
              : Text(text),
    );
  }
}
