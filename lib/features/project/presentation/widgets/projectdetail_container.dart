import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/widgets/detail_row_tile.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/home/<USER>/widgets/projectdetail_tile.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/task/data/firebase_task_repo.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProjectDetailContainer extends StatelessWidget {
  ProjectDetailContainer({
    super.key,
    required this.isMobile,
    required this.project,
  });

  bool isMobile;
  final ProjectModel project;

  @override
  Widget build(BuildContext context) {
    final projectCubit = context.read<ProjectCubit>();
    final userCubit = context.read<UserCubit>();
    return BlocProvider(
      create:
          (context) => TaskCubit(FirebaseTaskRepo())
            ..fetchProjectTaskAssignedToCurrentUser(
              FBAuth.auth.currentUser?.uid ?? "",
              project.docId,
            ),
      child: Container(
        padding: EdgeInsets.all(isMobile ? 0 : 30),
        decoration: BoxDecoration(
          color: AppColors.containerGreyColor,
          border: Border.all(color: AppColors.borderGrey),
          borderRadius: BorderRadius.circular(12),
        ),
        child:
            isMobile
                ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding:
                          isMobile
                              ? EdgeInsets.fromLTRB(15, 15, 15, 0)
                              : EdgeInsets.fromLTRB(30, 30, 30, 0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Project image or fallback
                          project.projectImg != null
                              ? ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: CachedNetworkImage(
                                  imageUrl: project.projectImg ?? "",
                                  height: 60,
                                  width: 60,
                                  fit: BoxFit.cover,
                                  placeholder:
                                      (context, url) => Container(
                                        width: 60,
                                        height: 60,
                                        alignment: Alignment.center,
                                        child: const CircularProgressIndicator(
                                          color: AppColors.primary,
                                          strokeWidth: 2,
                                        ),
                                      ),
                                  errorWidget:
                                      (context, url, error) =>
                                          Container(width: 60, height: 60),
                                ),
                              )
                              : Container(
                                decoration: BoxDecoration(
                                  color: getColorFromInput(
                                    project.projectTitle[0],
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                height: 60,
                                width: 60,
                                child: Center(
                                  child: Text(
                                    project.projectTitle[0].toUpperCase(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 30,
                                    ),
                                  ),
                                ),
                              ),
                          const SizedBox(width: 15),
                          // Title
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              project.projectStatus == "Finished"
                                  ? Text(
                                    project.projectTitle,
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  )
                                  : FutureBuilder<bool>(
                                    future: context
                                        .read<ProjectCubit>()
                                        .getLastSeen(project),
                                    builder: (context, snapshot) {
                                      final hasUpdates = snapshot.data ?? false;

                                      return Row(
                                        children: [
                                          Text(
                                            project.projectTitle,
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          if (hasUpdates) ...[
                                            const SizedBox(width: 6),
                                            DotIndicator(),
                                          ],
                                        ],
                                      );
                                    },
                                  ),

                              const SizedBox(height: 8),

                              Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 3,
                                  horizontal: 8,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: statusColor(project.projectStatus),
                                ),
                                child: Text(
                                  project.projectStatus,
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Spacer(),
                          BlocBuilder<TaskCubit, TaskState>(
                            builder: (context, state) {
                              return state.currentUserTask.length > 0
                                  ? Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      color: AppColors.secondary,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Text(
                                      state.currentUserTask.length.toString(),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                  : SizedBox();
                            },
                          ),
                        ],
                      ),
                    ),
                    // const SizedBox(height: ),
                    Container(
                      // color: Colors.red,
                      child: Theme(
                        data: ThemeData(dividerColor: Colors.transparent),
                        child: ExpansionTile(
                          clipBehavior: Clip.antiAlias,
                          // tilePadding: EdgeInsets.zero,
                          childrenPadding: EdgeInsets.fromLTRB(15, 0, 15, 15),
                          title: Text(
                            "View Details",
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,

                              children: [
                                Text(project.projectDesc),
                                const SizedBox(height: 15),
                                BlocBuilder<UserCubit, UserState>(
                                  builder: (context, state) {
                                    return DetailRowTile(
                                      title: "Supervisor ",
                                      value:
                                          projectCubit.getSupervisorName(
                                            project.userId,
                                            context,
                                          ) ??
                                          "",
                                    );
                                  },
                                ),
                                const SizedBox(height: 15),
                                DetailRowTile(
                                  title: "Start Date",
                                  value: project.createdAt?.goodDayDate() ?? "",
                                ),
                                const SizedBox(height: 15),
                                DetailRowTile(
                                  title: "End Date",
                                  value:
                                      project.complitionDate?.goodDayDate() ??
                                      "",
                                ),
                                const SizedBox(height: 15),
                                DetailRowTile(
                                  title: "Total Users",
                                  value: project.userId.length.toString(),
                                ),
                                const SizedBox(height: 15),
                                if (project.completedAt != null) ...[
                                  DetailRowTile(
                                    title: "Completed Date",
                                    value:
                                        project.completedAt?.goodDayDate() ??
                                        "",
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
                : Stack(
                  children: [
                    // Main content
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Project image or fallback
                            project.projectImg != null
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: CachedNetworkImage(
                                    imageUrl: project.projectImg ?? "",
                                    height: 60,
                                    width: 60,
                                    fit: BoxFit.cover,
                                    placeholder:
                                        (context, url) => Container(
                                          width: 60,
                                          height: 60,
                                          alignment: Alignment.center,
                                          child:
                                              const CircularProgressIndicator(
                                                color: AppColors.primary,
                                                strokeWidth: 2,
                                              ),
                                        ),
                                    errorWidget:
                                        (context, url, error) =>
                                            Container(width: 60, height: 60),
                                  ),
                                )
                                : Container(
                                  decoration: BoxDecoration(
                                    color: getColorFromInput(
                                      project.projectTitle[0],
                                    ),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  height: 60,
                                  width: 60,
                                  child: Center(
                                    child: Text(
                                      project.projectTitle[0].toUpperCase(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 30,
                                      ),
                                    ),
                                  ),
                                ),
                            const SizedBox(width: 15),
                            // Title
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  project.projectTitle,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(truncateText(project.projectDesc, 27)),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        BlocBuilder<UserCubit, UserState>(
                          builder: (context, state) {
                            return DetailRowTile(
                              title: "Supervisor ",
                              value:
                                  projectCubit.getSupervisorName(
                                    project.userId,
                                    context,
                                  ) ??
                                  "",
                            );
                          },
                        ),
                        const SizedBox(height: 15),
                        DetailRowTile(
                          title: "Start Date",
                          value: project.createdAt?.goodDayDate() ?? "",
                        ),
                        const SizedBox(height: 15),
                        DetailRowTile(
                          title: "End Date",
                          value: project.complitionDate?.goodDayDate() ?? "",
                        ),
                        const SizedBox(height: 15),
                        DetailRowTile(
                          title: "Total Users",
                          value: project.userId.length.toString(),
                        ),

                        const SizedBox(height: 15),
                        if (project.completedAt != null) ...[
                          DetailRowTile(
                            title: "Completed Date",
                            value: project.completedAt?.goodDayDate() ?? "",
                          ),
                        ],

                        // Row(
                        //   children: [Text("Supervisor"), Spacer(), Text("Matin")],
                        // ),
                      ],
                    ),

                    Positioned(
                      top: 0,
                      right: 0,
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 3,
                              horizontal: 8,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: statusColor(project.projectStatus),
                            ),
                            child: Text(
                              project.projectStatus,
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(height: 3),
                          BlocBuilder<TaskCubit, TaskState>(
                            builder: (context, state) {
                              return state.currentUserTask.length > 0
                                  ? Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      color: AppColors.secondary,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Text(
                                      state.currentUserTask.length.toString(),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                  : SizedBox();
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}
