import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class ProjectForm extends StatefulWidget {
  ProjectModel? editProject;
  bool isMobile;
  ProjectForm({Key? key, required this.editProject, required this.isMobile})
    : super(key: key);

  @override
  State<ProjectForm> createState() => _ProjectFormState();
}

class _ProjectFormState extends State<ProjectForm> {
  final controller = MultiSelectController<String>();
  @override
  void initState() {
    context.read<ProjectFormCubit>().initializeForm(widget.editProject);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final projectFormCubit = context.read<ProjectFormCubit>();
    final authCubit = context.read<AuthCubit>();
    return BlocConsumer<ProjectFormCubit, ProjectFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final projectFormCubit = context.read<ProjectFormCubit>();
        return Form(
          key: projectFormCubit.formKey,
          child: IgnorePointer(
            ignoring: state.isLoading,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          icon: Icon(CupertinoIcons.xmark),
                        ),
                        Text("Add Project", style: AppTextStyles.heading2),
                        Spacer(),
                        PrimaryButton(
                          isLoading: state.isLoading,
                          text: "Save",
                          onPressed: () {
                            projectFormCubit.submit(
                              widget.editProject,
                              context,
                            );
                          },

                          width: 100,
                          height: 36,
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 400,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 30,
                      children: [
                        CustomTextField(
                          controller: projectFormCubit.titleController,
                          hintText: "project title",
                          title: "Title *",
                        ),
                        CustomTextField(
                          controller: projectFormCubit.descController,
                          hintText: "project description",
                          title: "Description *",
                        ),
                        CustomTextField(
                          controller: projectFormCubit.addressController,
                          hintText: "project address",
                          title: "Address *",
                        ),

                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Assign Users *"),
                            SizedBox(height: 5),
                            BlocBuilder<UserCubit, UserState>(
                              builder: (context, state) {
                                final users =
                                    state.users
                                        .where((user) => user.role != 'admin')
                                        .toList();
                                if (state.users.isNotEmpty) {
                                  // final users = state.users;

                                  return MultiDropdown<String>(
                                    items: List.generate(users.length, (index) {
                                      return DropdownItem(
                                        label:
                                            "${users[index].name} (${users[index].role})",
                                        value: users[index].docId,
                                        selected: projectFormCubit
                                            .state
                                            .selectedAssignUser
                                            .contains(users[index].docId),
                                      );
                                    }),
                                    controller: controller,
                                    enabled: true,

                                    /// Match chips with your design
                                    chipDecoration: const ChipDecoration(
                                      backgroundColor: AppColors.secondary,
                                      labelStyle: TextStyle(
                                        color: AppColors.white,
                                      ),
                                      wrap: true,
                                      runSpacing: 10,
                                      spacing: 10,
                                    ),

                                    /// Main field styling (matches InputDecoration)
                                    fieldDecoration: FieldDecoration(
                                      padding: const EdgeInsets.all(16),
                                      hintText: "Assign to",
                                      hintStyle: const TextStyle(
                                        color: AppColors.grey2,
                                      ),
                                      // Border styling
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: AppColors.grey2,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: AppColors.primary,
                                          width: 2,
                                        ),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: Colors.red,
                                        ),
                                      ),
                                    ),

                                    /// Item selection icons
                                    dropdownItemDecoration:
                                        DropdownItemDecoration(
                                          selectedIcon: const Icon(
                                            Icons.check_box,
                                            color: Colors.green,
                                          ),
                                          disabledIcon: Icon(
                                            Icons.lock,
                                            color: Colors.grey.shade300,
                                          ),
                                          backgroundColor:
                                              AppColors.chipGreyColor,
                                        ),

                                    /// Validation
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please select a user';
                                      }
                                      return null;
                                    },

                                    /// Handle selection
                                    onSelectionChange: (selectedItems) {
                                      projectFormCubit.updateSelectedUser(
                                        selectedItems,
                                      );
                                    },
                                  );
                                }
                                return SizedBox();
                              },
                            ),
                          ],
                        ),

                        CustomDropDownField(
                          title: "Status *",
                          hintText: "project status",
                          initialValue: state.projectStatus,
                          validatorText: "Enter Project Status",
                          items: const [
                            DropdownMenuItem(
                              value: ProjectStatus.active,
                              child: Text(ProjectStatus.active),
                            ),
                            DropdownMenuItem(
                              value: ProjectStatus.finished,
                              child: Text(ProjectStatus.finished),
                            ),
                            DropdownMenuItem(
                              value: ProjectStatus.onHold,
                              child: Text(ProjectStatus.onHold),
                            ),
                          ],
                          onChanged: (value) {
                            projectFormCubit.selectStatus(value ?? "");
                          },
                        ),

                        CustomDateField(
                          hintText:
                              state.complitionDate != null
                                  ? state.complitionDate?.goodDayDate()
                                  : "project completion date",

                          title: "Completion Date *",
                          onTap: () {
                            projectFormCubit.selectCompletedDate(context);
                          },
                          initialValue:
                              state.complitionDate?.goodDayDate() ?? "",
                          validator: true,
                        ),

                        CustomTextField(
                          controller: projectFormCubit.clientNameController,
                          hintText: "project client name",
                          title: "Client Name *",
                        ),
                        CustomNumTextField(
                          controller: projectFormCubit.clientContactController,
                          hintText: "project client contact",
                          title: "Client Contact *",
                        ),
                        authCubit.state.currentUser?.role == 'admin'
                            ? CustomNumTextField(
                              controller:
                                  projectFormCubit.totalAmountController,
                              hintText: "project total amount",
                              title: "Total Amount *",
                            )
                            : SizedBox.shrink(),
                      ],
                    ),
                    SizedBox(height: 15),
                    SizedBox(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("Project Image *"),
                          SizedBox(height: 10),
                          state.selectedFile == null && state.dbFile == null
                              ? Row(
                                children: [
                                  Expanded(
                                    child: CustomOutlineFileUploadField(
                                      hintText: "Choose File",
                                      onTap: () async {
                                        projectFormCubit.pickFile(context);
                                      },
                                      prefixIcon: Icon(Icons.attach_file),
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Text("OR"),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: CustomOutlineFileUploadField(
                                      hintText: "Capture photo",
                                      onTap: () async {
                                        projectFormCubit.pickFileFromCamera(
                                          context,
                                        );
                                      },
                                      prefixIcon: Icon(
                                        CupertinoIcons.camera,
                                        color: AppColors.grey2,
                                      ),
                                    ),
                                  ),
                                ],
                              )
                              : Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        CustomOutlineFileUploadField(
                                          hintText: "Choose File",
                                          onTap: () async {
                                            projectFormCubit.pickFile(context);
                                          },
                                          prefixIcon: Icon(Icons.attach_file),
                                        ),
                                        const SizedBox(height: 3),
                                        Text("OR"),
                                        const SizedBox(height: 3),
                                        CustomOutlineFileUploadField(
                                          hintText: "Capture photo",
                                          onTap: () async {
                                            projectFormCubit.pickFileFromCamera(
                                              context,
                                            );
                                          },
                                          prefixIcon: Icon(
                                            CupertinoIcons.camera,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 30),
                                  Center(
                                    child: buildFilePreview(
                                      context: context,
                                      selectedFile: state.selectedFile,
                                      dbFile: state.dbFile,
                                      dbFileExt:
                                          widget.editProject?.projectImgExt,
                                      dbFileName:
                                          widget.editProject?.projectImgName,
                                      isEdit: true,
                                      onDelete: () {
                                        final isDbFile =
                                            state.selectedFile == null &&
                                            state.dbFile != null;
                                        context
                                            .read<ProjectFormCubit>()
                                            .deletPickFile(isDbFile);
                                      },
                                      onView: () {
                                        context
                                            .read<ProjectFormCubit>()
                                            .viewPickFile(
                                              state.dbFile,
                                              context,
                                              widget.editProject?.projectImgExt,
                                            );
                                      },
                                      isMessage: false,
                                    ),
                                  ),
                                ],
                              ),
                        ],
                      ),
                    ),
                    authCubit.state.currentUser?.role == 'admin'
                        ? Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 5,
                            horizontal: 5,
                          ),

                          child: Row(
                            children: [
                              Text("Transcations"),
                              Spacer(),
                              IconButton(
                                onPressed: () {
                                  projectFormCubit.addTransaction();
                                },
                                icon: Icon(CupertinoIcons.add),
                              ),
                            ],
                          ),
                        )
                        : SizedBox(),
                    authCubit.state.currentUser?.role == 'admin' &&
                            state.transcations.isNotEmpty
                        ? StaggeredGrid.extent(
                          maxCrossAxisExtent: 400,
                          mainAxisSpacing: 15,
                          crossAxisSpacing: 30,
                          children: [
                            ...List.generate(state.transcations.length, (
                              index,
                            ) {
                              return TextFormField(
                                decoration: InputDecoration(
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: Colors.grey,
                                    ), // Use your AppColors.grey2
                                  ),
                                  hintText: "add amount",
                                  hintStyle: TextStyle(
                                    color: Colors.grey,
                                  ), // Use your AppTextStyles.label
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  suffixIcon: IconButton(
                                    onPressed: () {
                                      projectFormCubit.removeTransaction(index);
                                    },
                                    icon: Icon(CupertinoIcons.delete),
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Enter amount';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'Enter a valid number';
                                  }
                                  return null;
                                },
                                controller: TextEditingController(
                                  text:
                                      state.transcations[index].amount
                                          .toString(),
                                ),
                                onChanged: (value) {
                                  state.transcations[index].amount =
                                      num.tryParse(value) ?? 0;
                                },
                                keyboardType: TextInputType.number,
                              );
                            }),
                          ],
                        )
                        : SizedBox(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
