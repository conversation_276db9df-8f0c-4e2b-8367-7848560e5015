import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/activity/data/firebase_activity_repo.dart';
import 'package:cp_associates/features/bill/data/firebase_bill_repo.dart';
import 'package:cp_associates/features/document/data/firebase_document_repo.dart';
import 'package:cp_associates/features/project/presentation/widgets/dasboard/desktop_projectlayout.dart';
import 'package:cp_associates/features/project/presentation/widgets/dasboard/mobile_projectlayout.dart';
import 'package:cp_associates/features/task/data/firebase_task_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_cubit.dart';
import 'package:cp_associates/features/bill/presentation/cubit/bill_cubit.dart';

class ProjectDetailsPage extends StatefulWidget {
  final String projectId;
  final Widget child;

  const ProjectDetailsPage({required this.projectId, required this.child});

  @override
  State<ProjectDetailsPage> createState() => _ProjectDetailsPageState();
}

class _ProjectDetailsPageState extends State<ProjectDetailsPage> {
  @override
  Widget build(BuildContext context) {
    // return Scaffold(appBar: AppBar());
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => ActivityCubit(FirebaseActivityRepo())),
        // BlocProvider(create: (_) => TaskCubit(FirebaseTaskRepo())),
        BlocProvider(
          create: (_) => DocumentCubit(documentRepo: FirebaseDocumentRepo()),
        ),
        BlocProvider(create: (_) => BillCubit(repo: FbBillRepo())),
      ],
      child: ResponsiveWid(
        desktop: DesktopProjectLayout(
          projectId: widget.projectId,
          child: widget.child,
        ),
        mobile: MobileProjectLayout(projectId: widget.projectId),
      ),
    );
  }
}
