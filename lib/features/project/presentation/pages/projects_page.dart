import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/home/<USER>/cubit/setting_cubit.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_tile.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class ProjectsPage extends StatefulWidget {
  const ProjectsPage({super.key});

  @override
  State<ProjectsPage> createState() => _ProjectsPageState();
}

class _ProjectsPageState extends State<ProjectsPage> {
  @override
  void initState() {
    super.initState();
    // Initialize with "All" filter (non-finished projects) immediately
    // This prevents the flash of "No Project Found"
    final cubit = context.read<ProjectCubit>();

    // Immediately set loading state to prevent "No Project Found" flash
    cubit.setLoadingState();

    // Then trigger the filter
    Future.microtask(() {
      cubit.filterProjects(filter: ProjectFilter.All);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProjectCubit, ProjectState>(
      builder: (context, state) {
        return ResponsiveWidCustom(
          mobile: BlocBuilder<ProjectCubit, ProjectState>(
            builder: (context, state) {
              return Scaffold(
                appBar: AppBar(
                  title: Text("Projects", style: AppTextStyles.appBarHeading),
                ),
                body: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // FILTER TOGGLES
                      Row(
                        children: [
                          filterProjectWidget(
                            label: ProjectFilter.All,
                            groupValue: state.selectedFilter,
                            context: context,
                            onTap: () {
                              context.read<ProjectCubit>().filterProjects(
                                filter: ProjectFilter.All,
                              );
                            },
                          ),
                          const SizedBox(width: 10),
                          filterProjectWidget(
                            label: ProjectFilter.finished,
                            groupValue: state.selectedFilter,
                            context: context,
                            onTap: () {
                              context.read<ProjectCubit>().filterProjects(
                                filter: ProjectFilter.finished,
                              );
                            },
                          ),
                        ],
                      ),

                      // FILTER TOGGLES IF SELECTED TYPE IS COMPLETED
                      if (state.selectedFilter == ProjectFilter.finished) ...[
                        SizedBox(height: 10),
                        Divider(),
                        SizedBox(height: 10),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              filterProjectWidget(
                                label: FinishedProjectFilter.Monthly,
                                groupValue: state.FinishedProjectselectedFilter,
                                context: context,
                                onTap: () {
                                  context
                                      .read<ProjectCubit>()
                                      .filterCompletedProjects(
                                        filterType:
                                            FinishedProjectFilter.Monthly,
                                      );
                                },
                              ),

                              const SizedBox(width: 10),
                              filterProjectWidget(
                                label: FinishedProjectFilter.Months3,
                                groupValue: state.FinishedProjectselectedFilter,
                                context: context,
                                onTap: () {
                                  context
                                      .read<ProjectCubit>()
                                      .filterCompletedProjects(
                                        filterType:
                                            FinishedProjectFilter.Months3,
                                      );
                                },
                              ),
                              const SizedBox(width: 10),
                              InkWell(
                                onTap: () async {
                                  final date = await showDateRangePicker(
                                    context: context,
                                    firstDate: DateTime(2023),
                                    lastDate: DateTime(2100),
                                  );
                                  if (date != null) {
                                    context
                                        .read<ProjectCubit>()
                                        .filterCompletedProjects(
                                          filterType:
                                              FinishedProjectFilter.Custom,
                                          customRange: date,
                                        );
                                  }
                                },
                                child: Icon(
                                  CupertinoIcons.calendar,
                                  size: 30,
                                  color:
                                      state.FinishedProjectselectedFilter ==
                                              FinishedProjectFilter.Custom
                                          ? AppColors.primary
                                          : AppColors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      SizedBox(height: 20),
                      Expanded(child: ProjectDetailTile(isMobile: true)),
                    ],
                  ),
                ),
              );
            },
          ),
          desktop: GestureDetector(
            onTap: () {
              final settingCubit = context.read<SettingCubit>();
              if (settingCubit.controller.isShowing) {
                settingCubit.controller.toggle();
              } else {
                null;
              }
            },
            child: Scaffold(
              backgroundColor: AppColors.containerGreyColor,
              appBar: PreferredSize(
                child: CommonAppBar(),
                preferredSize: Size.fromHeight(60),
              ),
              body: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 160,
                  vertical: 50,
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        InkWell(
                          onTap: () {
                            context.go(Routes.home);
                          },
                          child: Icon(Icons.arrow_back),
                        ),
                        SizedBox(width: 30),
                        Text(
                          "ALL PROJECTS",
                          style: AppTextStyles.appBarHeading,
                        ),
                      ],
                    ),
                    SizedBox(height: 30),
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 30,
                        horizontal: 10,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        // border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 15,
                        children: [
                          // Main filter tabs (All/Finished)
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30),
                            child: Row(
                              children: [
                                filterProjectWidget(
                                  label: ProjectFilter.All,
                                  groupValue: state.selectedFilter,
                                  context: context,
                                  onTap: () {
                                    context.read<ProjectCubit>().filterProjects(
                                      filter: ProjectFilter.All,
                                    );
                                  },
                                ),
                                const SizedBox(width: 10),
                                filterProjectWidget(
                                  label: ProjectFilter.finished,
                                  groupValue: state.selectedFilter,
                                  context: context,
                                  onTap: () {
                                    context.read<ProjectCubit>().filterProjects(
                                      filter: ProjectFilter.finished,
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),

                          // Sub-filters for finished projects
                          if (state.selectedFilter == ProjectFilter.finished)
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 30,
                              ),
                              child: Row(
                                children: [
                                  filterProjectWidget(
                                    label: FinishedProjectFilter.Monthly,
                                    groupValue:
                                        state.FinishedProjectselectedFilter,
                                    context: context,
                                    onTap: () {
                                      context
                                          .read<ProjectCubit>()
                                          .filterCompletedProjects(
                                            filterType:
                                                FinishedProjectFilter.Monthly,
                                          );
                                    },
                                  ),

                                  const SizedBox(width: 10),
                                  filterProjectWidget(
                                    label: FinishedProjectFilter.Months3,
                                    groupValue:
                                        state.FinishedProjectselectedFilter,
                                    context: context,
                                    onTap: () {
                                      context
                                          .read<ProjectCubit>()
                                          .filterCompletedProjects(
                                            filterType:
                                                FinishedProjectFilter.Months3,
                                          );
                                    },
                                  ),
                                  const SizedBox(width: 10),
                                  filterProjectWidget(
                                    label: FinishedProjectFilter.Custom,
                                    groupValue:
                                        state.FinishedProjectselectedFilter,
                                    context: context,
                                    onTap: () async {
                                      final date = await showDateRangePicker(
                                        context: context,
                                        firstDate: DateTime(2023),
                                        lastDate: DateTime(2100),
                                      );
                                      if (date != null) {
                                        context
                                            .read<ProjectCubit>()
                                            .filterCompletedProjects(
                                              filterType:
                                                  FinishedProjectFilter.Custom,
                                              customRange: date,
                                            );
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                          Divider(),
                          ProjectDetailTile(isMobile: false),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget filterProjectWidget({
    required String label,
    required String groupValue,
    required BuildContext context,
    VoidCallback? onTap,
  }) {
    final isSelected = label == groupValue;
    return GestureDetector(
      onTap:
          onTap ??
          () {
            context.read<ProjectCubit>().selectedFinishedProjectFilter(label);
          },
      child: Container(
        height: 40,
        padding: const EdgeInsets.only(right: 20),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.secondary : Colors.transparent,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Radio<String>(
              activeColor: AppColors.white,
              value: label,
              groupValue: groupValue,
              onChanged: (_) {
                if (onTap != null) {
                  onTap();
                } else {
                  context.read<ProjectCubit>().selectedFinishedProjectFilter(
                    label,
                  );
                }
              },
            ),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.white : Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
