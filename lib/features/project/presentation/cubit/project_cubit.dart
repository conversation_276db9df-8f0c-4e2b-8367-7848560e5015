import 'dart:async';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_cubit.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'project_state.dart';

class ProjectCubit extends Cubit<ProjectState> {
  final ProjectRepo repo;
  ProjectCubit(this.repo) : super(ProjectState.initial());
  StreamSubscription<List<ProjectModel>>? projectsStream;

  // fetch all project for admin
  void fetchProjectsForAdmin() {
    emit(state.copyWith(isLoading: true, message: "", projects: []));

    projectsStream?.cancel();

    projectsStream = repo.getProjectsForAdmin().listen(
      (projects) {
        if (!isClosed) {
          print("ProjectStreamAdmin-${projects.length}");

          // check if projectDetail is being displayed
          ProjectModel? currentDetail = state.projectDetail;
          ProjectModel? updatedDetail;

          if (currentDetail != null) {
            try {
              updatedDetail = projects.firstWhere(
                (project) => project.docId == currentDetail.docId,
              );
            } catch (_) {
              updatedDetail = null;
            }
          }

          emit(
            state.copyWith(
              projects: projects,
              isLoading: false,
              message: "",
              projectDetail: updatedDetail ?? currentDetail,
            ),
          );
        }
      },
      onError: (error) {
        if (!isClosed) {
          print(error.toString());
          emit(state.copyWith(isLoading: false, message: error.toString()));
        }
      },
    );
  }

  // fetch project detail
  void fetchProjectDetail(String id) async {
    emit(state.copyWith(isLoading: true, message: ""));

    try {
      // First try to find the project in the current state
      final list = state.filteredProjects + state.projects;
      ProjectModel? project;
      try {
        project = list.firstWhere((project) => project.docId == id);
      } catch (e) {
        project = null;
      }

      // If project is not found in current state, fetch it directly from repository
      if (project == null) {
        project = await repo.getProjectById(id);
      }

      emit(
        state.copyWith(projectDetail: project, isLoading: false, message: ""),
      );
    } catch (e) {
      print("Error in fetchProjectDetail: $e");
      emit(
        state.copyWith(
          message: "Failed to load project: ${e.toString()}",
          isLoading: false,
        ),
      );
    }
  }

  // fetch project of current user and createdBy
  void fetchProjectsOfCurrentUser(NotificationCubit notificationCubit) {
    emit(state.copyWith(isLoading: true, message: "", projects: []));

    projectsStream?.cancel();

    projectsStream = repo.getProjectsOfCurrentUser().listen(
      (projects) {
        if (!kIsWeb) {
          // firts unsubscribe from all topics

          // subscribe to topic for notification

          for (var project in projects) {
            notificationCubit.subscribeToTopic(project.docId.substring(0, 6));
          }
        }

        if (!isClosed) {
          // check if project detail is being displayed
          ProjectModel? currentDetail = state.projectDetail;
          ProjectModel? updatedDetail;

          if (currentDetail != null) {
            try {
              updatedDetail = projects.firstWhere(
                (project) => project.docId == currentDetail.docId,
              );
            } catch (_) {
              updatedDetail = null;
            }
          }
          emit(
            state.copyWith(
              projects: projects,
              isLoading: false,
              message: "",
              projectDetail: updatedDetail ?? currentDetail,
            ),
          );
          print("ProjectStreamUser------${projects.length}");
        }
      },
      onError: (error) {
        if (!isClosed) {
          print(error.toString());
          emit(state.copyWith(isLoading: false, message: error.toString()));
        }
      },
    );
  }

  // fetch all non finished project
  void fetchNonFinishedProjects() {
    emit(state.copyWith(isLoading: true, message: "", filteredProjects: []));
    repo
        .getAllNonFinishedProjects()
        .then((projects) {
          emit(state.copyWith(filteredProjects: projects, isLoading: false));
        })
        .catchError((error) {
          emit(state.copyWith(isLoading: false, message: error.toString()));
        });
  }

  // fetch project by id
  ProjectModel? fetchProjectById(String id) {
    final allProjects = state.projects + state.filteredProjects;
    try {
      final project = allProjects.firstWhere(
        (project) => project.docId == id,
        orElse: null,
      );
      return project;
    } catch (e) {
      return null;
    }
  }

  void deleteProject(String projectId) async {
    emit(state.copyWith(isLoading: true));

    await repo.deleteProject(projectId);
    emit(
      state.copyWith(isLoading: false, message: "Project deleted succesfully."),
    );
  }

  // check user have access to project
  void checkUserAccess(
    ProjectModel? project,
    String userId,
    BuildContext context,
  ) {
    final isAdmin =
        context.read<AuthCubit>().state.currentUser?.role == "admin";
    final isUserInProject = project?.userId.contains(userId) ?? false;
    final isProjectFinished = project?.projectStatus == ProjectStatus.finished;

    emit(
      state.copyWith(
        haveAccess: !isProjectFinished && (isUserInProject || isAdmin),
      ),
    );
  }

  // filter methods

  // filter active projects and finished
  Future<void> filterProjects({required String filter}) async {
    emit(state.copyWith(isLoading: true, selectedFilter: filter));
    try {
      if (filter == ProjectFilter.All) {
        final projects = await repo.getAllNonFinishedProjects();
        emit(
          state.copyWith(
            filteredProjects: projects,
            isLoading: false,
            message: "",
          ),
        );
      } else if (filter == ProjectFilter.finished) {
        final projects = await repo.getAllFinishedProject(
          DateTime.now().subtract(const Duration(days: 30)),
          DateTime.now(),
        );
        emit(
          state.copyWith(
            filteredProjects: projects,
            isLoading: false,
            message: "",
          ),
        );
      }
    } catch (e) {
      print(e.toString());
      emit(state.copyWith(isLoading: false, message: e.toString()));
    }
  }

  // filter completed projects
  Future<void> filterCompletedProjects({
    required String filterType,
    DateTimeRange? customRange,
  }) async {
    emit(
      state.copyWith(
        isLoading: true,
        FinishedProjectselectedFilter: filterType,
      ),
    );

    DateTime now = DateTime.now();
    DateTime start;
    DateTime end;

    if (filterType == FinishedProjectFilter.Monthly) {
      start = DateTime(now.year, now.month);
      end = DateTime(now.year, now.month + 1);
    } else if (filterType == FinishedProjectFilter.Months3) {
      start = DateTime(now.year, now.month - 2);
      end = DateTime(now.year, now.month + 1);
    } else if (filterType == FinishedProjectFilter.Custom &&
        customRange != null) {
      start = customRange.start;
      end = customRange.end.add(const Duration(days: 1)); // inclusive
    } else {
      emit(state.copyWith(isLoading: false, message: 'Invalid filter'));
      return;
    }

    try {
      final projects = await repo.getAllFinishedProject(start, end);
      emit(
        state.copyWith(
          filteredProjects: projects,
          isLoading: false,
          message: "",
        ),
      );
    } catch (e) {
      print(e.toString());
      emit(state.copyWith(isLoading: false, message: e.toString()));
    }
  }

  void selectedFinishedProjectFilter(String filter) {
    emit(state.copyWith(selectedFilter: filter));
  }

  void setLoadingState() {
    emit(state.copyWith(isLoading: true, message: "", filteredProjects: []));
  }

  // ui related methods
  void totalProjectCount() async {
    if (isClosed) return;
    emit(state.copyWith(isLoading: true, message: ""));
    await repo.getTotalProjectCount().then(
      (count) {
        if (!isClosed) {
          emit(state.copyWith(totalProjectCount: count, isLoading: false));
        }
      },
      onError: (error) {
        if (!isClosed) {
          emit(state.copyWith(message: error.toString(), isLoading: false));
        }
      },
    );
  }

  String getSupervisorName(List<String> userIds, BuildContext context) {
    final allUsers = context.read<UserCubit>().state.users;

    print("SupervisorUsers-${allUsers.length}");

    final supervisor = allUsers.firstWhere(
      (user) => userIds.contains(user.docId) && user.role == 'supervisor',
      orElse:
          () => UserModel(
            docId: "",
            name: "Not Assign",
            email: "",
            role: "",
            deleted: false,
          ),
    );

    return supervisor.name;
  }

  //unread notificationt functions
  Future<void> createLastSeen(String projectId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'last_seen_$projectId',
      DateTime.now().toIso8601String(),
    );
  }

  Future<bool> getLastSeen(ProjectModel project) async {
    // print("lastSeen: ${project.docId}--");
    final prefs = await SharedPreferences.getInstance();
    final seen = prefs.getString('last_seen_${project.docId}');

    final seenTime = DateTime.tryParse(seen ?? '');
    // print("lastSeen---: ${seenTime}");
    // print("updateLast---: ${project.lastUpdateAt}");
    // print(seenTime?.isBefore(project.lastUpdateAt));

    if (seenTime == null) {
      return false;
    } else if (seenTime.isBefore(project.lastUpdateAt)) {
      return true;
    } else {
      return false;
    }
  }

  Future<void> projectLastUpdateAt(String projectId) async {
    FBFireStore.projects.doc(projectId).update({
      'lastUpdateAt': DateTime.now(),
    });
  }

  @override
  Future<void> close() {
    projectsStream?.cancel();
    return super.close();
  }
}
