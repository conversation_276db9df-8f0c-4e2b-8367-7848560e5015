import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/mastertask/domain/entity/master_task_model.dart';
import 'package:cp_associates/features/mastertask/domain/repo/mastertask_repo.dart';
import 'package:meta/meta.dart';

part 'master_task_state.dart';

class MasterTaskCubit extends Cubit<MasterTaskState> {
  MasterTaskCubit(this.masterTaskRepo) : super(MasterTaskState.initial());
  MasterTaskRepo masterTaskRepo;
  StreamSubscription<List<MasterTaskModel>>? masterTaskStream;

  void fetchAllMasterTask() {
    emit(state.copyWith(isLoading: true, message: ""));

    masterTaskStream?.cancel();

    masterTaskStream = masterTaskRepo.getAllMasterTask().listen(
      (masterTask) {
        emit(
          state.copyWith(masterTask: masterTask, isLoading: false, message: ""),
        );
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch master task: ${error.toString()}",
          ),
        );
      },
    );
  }

  void deleteMasterTask(String masterTaskId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await masterTaskRepo.deleteMasterTask(masterTaskId);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Master Task deleted successfully",
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to delete master task: ${e.toString()}",
        ),
      );
    }
  }
}
