import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/mastertask/domain/entity/master_task_model.dart';
import 'package:cp_associates/features/mastertask/domain/repo/mastertask_repo.dart';
import 'package:flutter/cupertino.dart';

part 'master_task_form_state.dart';

class MasterTaskFormCubit extends Cubit<MasterTaskFormState> {
  MasterTaskRepo masterTaskRepo;
  MasterTaskFormCubit(this.masterTaskRepo)
    : super(MasterTaskFormState.initial());

  final titleController = TextEditingController();
  final descController = TextEditingController();
  final durationController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void initializeForm(MasterTaskModel? editMasterTask) {
    if (editMasterTask != null) {
      titleController.text = editMasterTask.title;
      descController.text = editMasterTask.desc;
      durationController.text = editMasterTask.duration.toString();
      emit(state.copyWith(selectedAssignUser: editMasterTask.assignTo));
    } else {
      emit(MasterTaskFormState.initial());
    }
  }

  void selectAssignUser(String assignUser) {
    emit(state.copyWith(selectedAssignUser: assignUser));
  }

  void submit(MasterTaskModel? editMasterTask, BuildContext context) async {
    if (formKey.currentState?.validate() ?? false) {
      emit(state.copyWith(isLoading: true, message: ''));
      try {
        final masterTask = MasterTaskModel(
          docId: editMasterTask?.docId ?? "",
          createdAt: editMasterTask?.createdAt ?? DateTime.now(),
          createdBy: FBAuth.auth.currentUser?.uid ?? "",
          title: titleController.text,
          desc: descController.text,
          assignTo: state.selectedUser,
          duration: int.tryParse(durationController.text) ?? 0,
        );

        if (editMasterTask == null) {
          await masterTaskRepo.createMasterTask(masterTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "New Master Task created successfully",
            ),
          );
          Navigator.of(context).pop();
        } else {
          await masterTaskRepo.updateMasterTask(masterTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "Update Master Task successfully",
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        emit(state.copyWith(isLoading: false, message: e.toString()));
      }
    }
  }
}
