part of 'master_task_form_cubit.dart';

@immutable
class MasterTaskFormState {
  final String message;
  final bool isLoading;
  final String selectedUser;
  // final num duration;

  MasterTaskFormState({
    required this.message,
    required this.isLoading,
    required this.selectedUser,
    // required this.duration,
  });

  factory MasterTaskFormState.initial() {
    return MasterTaskFormState(
      message: '',
      isLoading: false,
      selectedUser: '',
      // duration: 0,
    );
  }

  MasterTaskFormState copyWith({
    String? message,
    bool? isLoading,
    String? selectedAssignUser,
    // num? duration,
  }) {
    return MasterTaskFormState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      selectedUser: selectedAssignUser ?? this.selectedUser,
      // duration: duration ?? this.duration,
    );
  }
}
