import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/mastertask/domain/entity/master_task_model.dart';
import 'package:cp_associates/features/mastertask/presentation/cubit/master_task_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class MasterTaskForm extends StatefulWidget {
  MasterTaskModel? editMasterTask;
  MasterTaskForm({super.key, required this.editMasterTask});

  @override
  State<MasterTaskForm> createState() => _MasterTaskFormState();
}

class _MasterTaskFormState extends State<MasterTaskForm> {
  final controller = MultiSelectController<String>();

  @override
  void initState() {
    context.read<MasterTaskFormCubit>().initializeForm(widget.editMasterTask);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final masterTaskFormCubit = context.read<MasterTaskFormCubit>();
    return BlocConsumer<MasterTaskFormCubit, MasterTaskFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return SafeArea(
          child: SingleChildScrollView(
            child: IgnorePointer(
              ignoring: state.isLoading,
              child: Form(
                key: masterTaskFormCubit.formKey,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          IconButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            icon: Icon(CupertinoIcons.xmark),
                          ),
                          Text(
                            "Add Master Task",
                            style: AppTextStyles.heading2,
                          ),
                          Spacer(),
                          PrimaryButton(
                            isLoading: state.isLoading,
                            text: "Save",
                            onPressed: () {
                              masterTaskFormCubit.submit(
                                widget.editMasterTask,
                                context,
                              );
                            },

                            width: 100,
                            height: 36,
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 400,
                        mainAxisSpacing: 15,
                        crossAxisSpacing: 30,
                        children: [
                          CustomTextField(
                            controller: masterTaskFormCubit.titleController,
                            hintText: "task name",
                            title: "Title *",
                          ),

                          CustomDescTextField(
                            maxChars: 28,
                            controller: masterTaskFormCubit.descController,
                            hintText: "task desc",
                            title: "Description * (max 28 characters)",
                            validator: true,
                          ),
                          BlocBuilder<UserCubit, UserState>(
                            builder: (context, userState) {
                              if (userState.users.isNotEmpty) {
                                final users = userState.users;

                                return CustomDropDownField(
                                  title: "Assign Users *",
                                  hintText: "Assign to",
                                  initialValue:
                                      state.selectedUser.isEmpty
                                          ? null
                                          : state.selectedUser,
                                  validatorText: "Please select a user",
                                  
                                  items:
                                      users.map((user) {
                                        return DropdownMenuItem<String>(

                                          value: user.docId,
                                          child: Text(user.name),
                                        );
                                      }).toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      masterTaskFormCubit.selectAssignUser(
                                        value,
                                      );
                                    }
                                  },
                                );
                              } else {
                                return const Text("No users available");
                              }
                            },
                          ),
                          CustomNumTextField(
                            controller: masterTaskFormCubit.durationController,
                            hintText: "task duration",
                            title: "Duration* (in days)",
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
