import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/features/mastertask/presentation/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/mastertask/presentation/cubit/master_task_form_cubit.dart';
import 'package:cp_associates/features/mastertask/presentation/widget/mastertask_form.dart';
import 'package:cp_associates/features/mastertask/presentation/widget/mastertask_tile.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MasterTaskDialog extends StatelessWidget {
  const MasterTaskDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(maxWidth: 600, maxHeight: 800),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Text(
                    "Master Task",
                    style: AppTextStyles.appBarHeading.copyWith(fontSize: 25),
                  ),
                  Spacer(),

                  AddBtn(
                    text: "Add Task",
                    onPressed: () {
                      Navigator.pop(context);
                      showDialog(
                        // isScrollControlled: true,
                        context: context,
                        builder: (context) {
                          return Dialog(
                            child: Container(
                              constraints: BoxConstraints(maxWidth: 400),
                              padding: EdgeInsets.symmetric(
                                vertical: 10,
                                horizontal: 5,
                              ),
                              child: BlocProvider(
                                create:
                                    (context) => MasterTaskFormCubit(
                                      context
                                          .read<MasterTaskCubit>()
                                          .masterTaskRepo,
                                    ),
                                child: MasterTaskForm(editMasterTask: null),
                              ),
                            ),
                          );
                        },
                      );
                    },

                    color: AppColors.secondary,
                  ),
                  SizedBox(width: 30),
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(CupertinoIcons.xmark),
                  ),
                ],
              ),
              SizedBox(height: 40),
              MasterTaskTile(),
            ],
          ),
        ),
      ),
    );
  }
}
