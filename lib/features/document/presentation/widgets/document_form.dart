import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/presentation/cubit/documentform_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DocumentForm extends StatefulWidget {
  final DocumentModel? editDocument;
  final String projectId;

  const DocumentForm({Key? key, required this.projectId, this.editDocument})
    : super(key: key);

  @override
  State<DocumentForm> createState() => _DocumentFormState();
}

class _DocumentFormState extends State<DocumentForm> {
  @override
  void initState() {
    super.initState();
    // final documentFormCubit = context.read<DocumentFormCubit>();
    context.read<DocumentFormCubit>().initializeForm(widget.editDocument);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DocumentFormCubit, DocumentFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final documentFormCubit = context.read<DocumentFormCubit>();

        return SingleChildScrollView(
          child: IgnorePointer(
            ignoring: state.isLoading,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
              child: Form(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(CupertinoIcons.xmark),
                        ),
                        Text(
                          widget.editDocument != null
                              ? "Edit Document"
                              : "Add Document",
                          style: AppTextStyles.heading2,
                        ),
                        Spacer(),
                        PrimaryButton(
                          isLoading: state.isLoading,
                          text: widget.editDocument != null ? "Update" : "Save",
                          onPressed: () {
                            documentFormCubit.submit(
                              context: context,
                              projectId: widget.projectId,
                              editDocument: widget.editDocument,
                            );
                          },

                          height: 36,
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    CustomTextField(
                      controller: documentFormCubit.nameController,
                      hintText: "document Name",
                      title: "Name *",
                    ),
                    const SizedBox(height: 20),
                    state.selectedFile == null && state.dbFile == null
                        ? Row(
                          children: [
                            Expanded(
                              child: CustomOutlineFileUploadField(
                                hintText: "Choose File",
                                onTap: () async {
                                  documentFormCubit.pickFile(context);
                                },
                                prefixIcon: Icon(Icons.attach_file),
                              ),
                            ),
                            const SizedBox(width: 10),
                            Text("OR"),
                            const SizedBox(width: 10),
                            Expanded(
                              child: CustomOutlineFileUploadField(
                                hintText: "Capture photo",
                                onTap: () async {
                                  documentFormCubit.pickFileFromCamera(context);
                                },
                                prefixIcon: Icon(
                                  CupertinoIcons.camera,
                                  color: AppColors.grey2,
                                ),
                              ),
                            ),
                          ],
                        )
                        : Row(
                          crossAxisAlignment:
                              CrossAxisAlignment
                                  .center, // Ensures vertical centering
                          children: [
                            // Left side: File Preview (centered vertically)
                            Center(
                              child: buildFilePreview(
                                context: context,
                                selectedFile: state.selectedFile,
                                dbFile: state.dbFile,
                                dbFileExt: widget.editDocument?.docExtenstion,
                                dbFileName: widget.editDocument?.docFileName,
                                isEdit: true,
                                onDelete: () {
                                  final isDbFile =
                                      state.selectedFile == null &&
                                      state.dbFile != null;
                                  context
                                      .read<DocumentFormCubit>()
                                      .deletPickFile(isDbFile);
                                },
                                onView: () {
                                  context
                                      .read<DocumentFormCubit>()
                                      .viewPickFile(
                                        state.dbFile,
                                        context,
                                        widget.editDocument?.docExtenstion,
                                      );
                                },
                                isMessage: false,
                              ),
                            ),

                            const SizedBox(width: 20),

                            // Right side: Two vertical buttons
                            Expanded(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CustomOutlineFileUploadField(
                                    hintText: "Choose File",
                                    onTap: () async {
                                      documentFormCubit.pickFile(context);
                                    },
                                    prefixIcon: Icon(Icons.attach_file),
                                  ),
                                  const SizedBox(height: 3),
                                  Text("OR"),
                                  const SizedBox(height: 3),
                                  CustomOutlineFileUploadField(
                                    hintText: "Capture photo",
                                    onTap: () async {
                                      documentFormCubit.pickFileFromCamera(
                                        context,
                                      );
                                    },
                                    prefixIcon: Icon(CupertinoIcons.camera),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                    SizedBox(height: 20),
                    if (state.errorMessage != null)
                      Text(
                        state.errorMessage!,
                        style: TextStyle(color: Colors.red),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
