import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/domain/repo/document_repo.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
part 'documentform_state.dart';

class DocumentFormCubit extends Cubit<DocumentFormState> {
  final DocumentRepo documentRepo;

  DocumentFormCubit(this.documentRepo) : super(DocumentFormState.initial());

  final nameController = TextEditingController();

  final formKey = GlobalKey<FormState>();

  // Initialize form
  void initializeForm(DocumentModel? editDocument) {
    emit(state.copyWith(message: "", isLoading: true));
    if (editDocument != null) {
      nameController.text = editDocument.documentName;

      emit(
        state.copyWith(
          dbFile: editDocument.documentUrl,
          isLoading: false,
          selectedFile: null,
        ),
      );
    } else {
      emit(DocumentFormState.initial());
    }
  }

  // Files related methods

  // Pick file
  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  // Pick file from camera
  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  // View file
  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  // Delete file
  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  // Submit
  Future<void> submit({
    required BuildContext context,
    required String projectId,
    DocumentModel? editDocument,
  }) async {
    final projectCubit = context.read<ProjectCubit>();
    if (state.isLoading) {
      return;
    }

    emit(state.copyWith(isLoading: true, message: "", errorMessage: null));

    final name = nameController.text.trim();

    if (name.isEmpty ||
        (state.selectedFile == null && editDocument?.documentUrl == null)) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: "Please fill all fields and upload a document",
        ),
      );
      return;
    }

    try {
      final fileUrl =
          state.selectedFile != null
              ? await FirebaseStorageRepo().uploadDocumentsFile(
                state.selectedFile!,
              )
              : editDocument?.documentUrl;

      final doc = DocumentModel(
        docId: editDocument?.docId ?? "",
        documentName: name,
        documentUrl: fileUrl ?? "",
        docExtenstion:
            state.selectedFile?.extension ?? editDocument?.docExtenstion ?? "",
        docFileName:
            state.selectedFile?.name ?? editDocument?.docFileName ?? "",
        projectId: projectId,
        uploadAt: DateTime.now(),
        uploadBy: FBAuth.auth.currentUser?.uid ?? "",
      );

      if (editDocument != null) {
        await documentRepo.updateDocument(doc);
        emit(
          state.copyWith(
            isLoading: false,
            message: "Document updated successfully",
          ),
        );
        Navigator.pop(context);

        // project last update
        projectCubit.projectLastUpdateAt(projectId);
        projectCubit.createLastSeen(projectId);
        return;
      } else {
        await documentRepo.uploadDocument(doc);
        emit(
          state.copyWith(
            isLoading: false,
            message: "Document uploaded successfully",
          ),
        );
        Navigator.pop(context);
        // project last update
        projectCubit.projectLastUpdateAt(projectId);
        projectCubit.createLastSeen(projectId);
        return;
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to upload document: ${e.toString()}",
        ),
      );
    }
  }
}
