part of 'documentform_cubit.dart';

class DocumentFormState {
  // final TextEditingController nameController;
  final SelectedImage? selectedFile;
  final String? dbFile;
  final bool isLoading;
  final String message;
  final String? errorMessage;

  DocumentFormState({
    // required this.nameController,
    required this.dbFile,
    this.selectedFile,
    required this.isLoading,
    required this.message,
    this.errorMessage,
  });

  factory DocumentFormState.initial() {
    return DocumentFormState(
      // nameController: TextEditingController(),
      dbFile: null,
      selectedFile: null,
      isLoading: false,
      message: '',
      errorMessage: null,
    );
  }

  DocumentFormState copyWith({
    // TextEditingController? nameController,
    dynamic dbFile,
    dynamic selectedFile,
    bool? isLoading,
    String? message,
    String? errorMessage,
  }) {
    return DocumentFormState(
      // nameController: nameController ?? this.nameController,
      dbFile: dbFile is bool ? null : (dbFile ?? this.dbFile),
      selectedFile:
          selectedFile is bool ? null : (selectedFile ?? this.selectedFile),
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
