part of 'billform_cubit.dart';

class BillFormState {
  final String? dbFile;
  final SelectedImage? selectedFile;
  final bool isLoading;
  final String message;
  final String? errorMessage;

  BillFormState({
    this.dbFile,
    this.selectedFile,
    required this.isLoading,
    required this.message,
    this.errorMessage,
  });

  factory BillFormState.initial() {
    return BillFormState(
      dbFile: null,
      selectedFile: null,
      isLoading: false,
      message: '',
      errorMessage: null,
    );
  }

  BillFormState copyWith({
    dynamic dbFile,
    dynamic selectedFile,
    bool? isLoading,
    String? message,
    String? errorMessage,
  }) {
    return BillFormState(
      dbFile: dbFile is bool ? null : (dbFile ?? this.dbFile),
      selectedFile:
          selectedFile is bool ? null : (selectedFile ?? this.selectedFile),
      errorMessage: errorMessage ?? this.errorMessage,
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
    );
  }
}
