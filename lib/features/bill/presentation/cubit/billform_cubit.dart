import 'dart:async';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/domain/repo/bill_repo.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'billform_state.dart';

class BillFormCubit extends Cubit<BillFormState> {
  final BillRepo billRepo;

  BillFormCubit(this.billRepo) : super(BillFormState.initial());

  final nameController = TextEditingController();
  final amountController = TextEditingController();
  final descController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Initialize form
  void initializeForm(BillModel? editBill) {
    emit(state.copyWith(message: "", isLoading: true));
    if (editBill != null) {
      nameController.text = editBill.billName;
      amountController.text = editBill.billAmount.toString();
      descController.text = editBill.billDesc ?? "";

      emit(
        state.copyWith(
          dbFile: editBill.billUrl,
          isLoading: false,
          selectedFile: null,
        ),
      );
    } else {
      emit(BillFormState.initial());
    }
  }

  // Files related methods
  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  // Pick file from camera
  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  // View file
  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  // Delete file
  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  // Submit
  Future<void> submit({
    required BuildContext context,
    required String projectId,
    BillModel? editBill,
  }) async {
    final projectCubit = context.read<ProjectCubit>();

    if (state.isLoading) {
      return;
    }
    if (formKey.currentState?.validate() ?? false) {
      if (state.selectedFile == null && editBill?.billUrl == null) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: "Please upload a bill *",
          ),
        );

        return;
      }
      emit(state.copyWith(isLoading: true, message: ""));
      try {
        final fileUrl =
            state.selectedFile != null
                ? await FirebaseStorageRepo().uploadBillsFile(
                  state.selectedFile!,
                )
                : state.dbFile;

        final bill = BillModel(
          docId: editBill?.docId ?? "",
          billName: nameController.text.trim(),
          billAmount: num.tryParse(amountController.text.trim()) ?? 0,
          billDesc: descController.text.trim(),
          billUrl: fileUrl ?? "",
          billFileName:
              state.selectedFile?.name ?? editBill?.billFileName ?? "",
          billExtenstion:
              state.selectedFile?.extension ?? editBill?.billExtenstion ?? "",
          projectId: projectId,
          uploadAt: DateTime.now(),
          uploadBy: FBAuth.auth.currentUser?.uid ?? "",
        );

        if (editBill != null) {
          await billRepo.updateBill(bill);
          emit(
            BillFormState.initial().copyWith(
              message: "Bill Update successfully",
              isLoading: false,
            ),
          );

          // project last update
          projectCubit.projectLastUpdateAt(projectId);
          projectCubit.createLastSeen(projectId);
          Navigator.pop(context);
          return;
        } else {
          await billRepo.uploadBill(bill);
          emit(
            BillFormState.initial().copyWith(
              message: "Bill Upload successfully",
              isLoading: false,
            ),
          );
          Navigator.pop(context);

          // project last update
          projectCubit.projectLastUpdateAt(projectId);
          projectCubit.createLastSeen(projectId);
          return;
        }
      } catch (e) {
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to upload bill: ${e.toString()}",
          ),
        );
      }
    }
  }
}
