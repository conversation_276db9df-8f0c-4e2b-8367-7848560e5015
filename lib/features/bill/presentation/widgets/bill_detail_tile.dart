import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:flutter/material.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/presentation/cubit/bill_cubit.dart';
import 'package:cp_associates/features/bill/presentation/cubit/billform_cubit.dart';
import 'package:cp_associates/features/bill/presentation/widgets/bill_form.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class BillDetailTile extends StatefulWidget {
  String projectId;
  bool isMobile;

  BillDetailTile({super.key, required this.projectId, required this.isMobile});

  @override
  State<BillDetailTile> createState() => _BillDetailTileState();
}

class _BillDetailTileState extends State<BillDetailTile> {
  void initState() {
    context.read<BillCubit>().fetchBills(widget.projectId);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final billCubit = context.read<BillCubit>();
    return BlocBuilder<BillCubit, BillState>(
      builder: (context, state) {
        print("Bill Cubit build");
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.bills.isNotEmpty) {
          List<BillModel> filteredBills = [];
          if (state.selectedType == docTypes.All) {
            filteredBills = state.bills;
          } else if (state.selectedType == docTypes.PDF) {
            filteredBills =
                state.bills
                    .where(
                      (bill) =>
                          bill.billExtenstion.toLowerCase() ==
                          docTypes.PDF.toLowerCase(),
                    )
                    .toList();
          } else if (state.selectedType == docTypes.Images) {
            filteredBills =
                state.bills
                    .where(
                      (bill) => imageExtensions.contains(
                        bill.billExtenstion.toLowerCase(),
                      ),
                    )
                    .toList();
          } else {
            filteredBills =
                state.bills
                    .where(
                      (bill) =>
                          !imageExtensions.contains(
                            bill.billExtenstion.toLowerCase(),
                          ) &&
                          bill.billExtenstion.toLowerCase() !=
                              docTypes.PDF.toLowerCase(),
                    )
                    .toList();
          }
          return filteredBills.isEmpty
              ? Center(child: Text("No Bill Found"))
              : SingleChildScrollView(
                child: StaggeredGrid.extent(
                  maxCrossAxisExtent: widget.isMobile ? 530 : 120,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 30,

                  children: [
                    ...List.generate(filteredBills.length, (index) {
                      final BillModel bill = filteredBills[index];
                      return TransparentInkWell(
                        onTap: () {
                          billCubit.viewBill(bill, context);
                        },
                        onDoubleTap: () async {
                          kIsWeb
                              ? showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 500,
                                      child: BlocProvider(
                                        create:
                                            (context) =>
                                                BillFormCubit(billCubit.repo),
                                        child: BillForm(
                                          projectId: widget.projectId,
                                          editBill: bill,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              )
                              : showModalBottomSheet(
                                isScrollControlled: true,
                                context: context,
                                builder: (context) {
                                  return Padding(
                                    padding: MediaQuery.of(context).viewInsets,
                                    child: BlocProvider(
                                      create:
                                          (context) =>
                                              BillFormCubit(billCubit.repo),
                                      child: BillForm(
                                        projectId: widget.projectId,
                                        editBill: bill,
                                      ),
                                    ),
                                  );
                                },
                              );
                        },
                        onLongPress: () {
                          showConfirmDeletDialog(context, () {
                            billCubit.deleteBill(bill.docId);
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.only(
                            bottom: state.bills.length - 1 == index ? 50 : 0,
                          ),
                          child:
                              widget.isMobile
                                  ? Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(20),
                                        decoration: BoxDecoration(
                                          color: AppColors.chipGreyColor,
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border: Border.all(
                                            color: AppColors.borderGrey,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.receipt_long,
                                          size: 24,
                                        ),
                                      ),
                                      SizedBox(width: 15),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "₹ ${bill.billAmount.toString()}",
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                          Text(
                                            bill.billName,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                          SizedBox(height: 5),
                                          if (bill.billDesc != null) ...[
                                            Text(
                                              bill.billDesc ?? "",
                                              style: TextStyle(
                                                color: AppColors.grey2,
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                      Spacer(),
                                      Text(
                                        bill.uploadAt.goodDayDate(),
                                        style: TextStyle(
                                          color: AppColors.grey2,
                                        ),
                                      ),
                                    ],
                                  )
                                  : Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          vertical: 50,
                                        ),
                                        // padding: EdgeInsets.all(50),
                                        decoration: BoxDecoration(
                                          color: AppColors.chipGreyColor,
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border: Border.all(
                                            color: AppColors.borderGrey,
                                          ),
                                        ),
                                        child: Center(
                                          child: Icon(
                                            Icons.receipt_long,
                                            size: 25,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10),
                                      Text(
                                        "₹ ${bill.billAmount.toString()} - ${bill.billName}",
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                      SizedBox(height: 5),
                                      Text(bill.uploadAt.goodDayDate()),
                                      SizedBox(height: 5),
                                      if (bill.billDesc != null) ...[
                                        Text(
                                          bill.billDesc ?? "",
                                          style: TextStyle(
                                            color: AppColors.grey2,
                                          ),
                                        ),
                                      ],

                                      SizedBox(height: 5),
                                    ],
                                  ),
                        ),
                      );
                    }),
                  ],
                ),
              );
        } else if (state.bills.isEmpty) {
          return Center(child: Text("No Bill Found"));
        } else if (state.message.isNotEmpty) {
          return Center(child: Text(state.message.toString()));
        } else {
          return Center();
        }
      },
    );
  }
}
