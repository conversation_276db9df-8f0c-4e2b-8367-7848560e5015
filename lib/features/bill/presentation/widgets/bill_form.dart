import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/presentation/cubit/billform_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class BillForm extends StatefulWidget {
  BillForm({super.key, required this.projectId, this.editBill});
  final BillModel? editBill;
  final String projectId;

  @override
  State<BillForm> createState() => _BillFormState();
}

class _BillFormState extends State<BillForm> {
  void initState() {
    super.initState();
    context.read<BillFormCubit>().initializeForm(widget.editBill);
  }

  @override
  Widget build(BuildContext context) {
    final billFormCubit = context.read<BillFormCubit>();

    return BlocConsumer<BillFormCubit, BillFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return IgnorePointer(
          ignoring: state.isLoading,
          child: Form(
            key: billFormCubit.formKey,
            child: StaggeredGrid.extent(
              maxCrossAxisExtent: 530,
              mainAxisSpacing: 15,
              crossAxisSpacing: 30,
              children: [
                ...List.generate(1, (index) {
                  return SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15,
                          vertical: 20,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              children: [
                                IconButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  icon: Icon(CupertinoIcons.xmark),
                                ),
                                Text(
                                  widget.editBill != null
                                      ? "Edit Bill"
                                      : "Add Bill",
                                  style: AppTextStyles.heading2,
                                ),
                                Spacer(),
                                PrimaryButton(
                                  isLoading: state.isLoading,
                                  text:
                                      widget.editBill != null
                                          ? "Update"
                                          : "Save",
                                  onPressed: () {
                                    billFormCubit.submit(
                                      editBill: widget.editBill,
                                      projectId: widget.projectId,
                                      context: context,
                                    );
                                  },

                                  height: 36,
                                ),
                              ],
                            ),
                            SizedBox(height: 15),
                            CustomTextField(
                              controller: billFormCubit.nameController,
                              hintText: "bill name",
                              title: "Name *",
                            ),
                            SizedBox(height: 15),
                            CustomNumTextField(
                              controller: billFormCubit.amountController,
                              hintText: "bill amount",
                              title: "Amount *",
                            ),
                            SizedBox(height: 15),
                            CustomDescTextField(
                              controller: billFormCubit.descController,
                              hintText: "bill description",
                              title: "Description (max 28 characters) *",
                              maxChars: 28,
                            ),
                            SizedBox(height: 15),
                            state.selectedFile == null && state.dbFile == null
                                ? Row(
                                  children: [
                                    Expanded(
                                      child: CustomOutlineFileUploadField(
                                        hintText: "Choose File",
                                        onTap: () async {
                                          billFormCubit.pickFile(context);
                                        },
                                        prefixIcon: Icon(Icons.attach_file),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Text("OR"),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: CustomOutlineFileUploadField(
                                        hintText: "Capture photo",
                                        onTap: () async {
                                          billFormCubit.pickFileFromCamera(
                                            context,
                                          );
                                        },
                                        prefixIcon: Icon(
                                          CupertinoIcons.camera,
                                          color: AppColors.grey2,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                                : Row(
                                  crossAxisAlignment:
                                      CrossAxisAlignment
                                          .center, // Ensures vertical centering
                                  children: [
                                    // Left side: File Preview (centered vertically)
                                    Center(
                                      child: buildFilePreview(
                                        context: context,
                                        selectedFile: state.selectedFile,
                                        dbFile: state.dbFile,
                                        dbFileExt:
                                            widget.editBill?.billExtenstion,
                                        dbFileName:
                                            widget.editBill?.billFileName,
                                        isEdit: true,
                                        onDelete: () {
                                          final isDbFile =
                                              state.selectedFile == null &&
                                              state.dbFile != null;
                                          context
                                              .read<BillFormCubit>()
                                              .deletPickFile(isDbFile);
                                        },
                                        onView: () {
                                          context
                                              .read<BillFormCubit>()
                                              .viewPickFile(
                                                state.dbFile,
                                                context,
                                                widget.editBill?.billExtenstion,
                                              );
                                        },
                                        isMessage: false,
                                      ),
                                    ),

                                    const SizedBox(width: 20),

                                    // Right side: Two vertical buttons
                                    Expanded(
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          CustomOutlineFileUploadField(
                                            hintText: "Choose File",
                                            onTap: () async {
                                              billFormCubit.pickFile(context);
                                            },
                                            prefixIcon: Icon(Icons.attach_file),
                                          ),
                                          const SizedBox(height: 3),
                                          Text("OR"),
                                          const SizedBox(height: 3),
                                          CustomOutlineFileUploadField(
                                            hintText: "Capture photo",
                                            onTap: () async {
                                              billFormCubit.pickFileFromCamera(
                                                context,
                                              );
                                            },
                                            prefixIcon: Icon(
                                              CupertinoIcons.camera,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                            SizedBox(height: 15),
                            state.errorMessage != null
                                ? Text(
                                  state.errorMessage!,
                                  style: TextStyle(color: Colors.red),
                                )
                                : SizedBox.shrink(),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }
}
