import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/features/mastertask/presentation/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class TaskForm extends StatefulWidget {
  TaskModel? editTask;
  String projectId;
  bool isMobile;
  TaskForm({
    super.key,
    required this.projectId,
    required this.editTask,
    required this.isMobile,
  });

  @override
  State<TaskForm> createState() => _TaskFormState();
}

class _TaskFormState extends State<TaskForm> {
  final controller = MultiSelectController<String>();

  @override
  void initState() {
    context.read<TaskFormCubit>().initializeForm(widget.editTask);
    context.read<MasterTaskCubit>().fetchAllMasterTask();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final taskFormCubit = context.read<TaskFormCubit>();
    return BlocConsumer<TaskFormCubit, TaskFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return IgnorePointer(
          ignoring: state.isLoading,
          child: SingleChildScrollView(
            child: Form(
              key: taskFormCubit.formKey,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          icon: Icon(CupertinoIcons.xmark),
                        ),
                        Text("Add Task", style: AppTextStyles.formtitle),
                        Spacer(),
                        PrimaryButton(
                          isLoading: state.isLoading,
                          text: "Save",
                          onPressed: () {
                            taskFormCubit.submit(
                              widget.editTask,
                              widget.projectId,
                              context,
                            );
                          },

                          width: 100,
                          height: 36,
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 400,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 30,
                      children: [
                        CustomDropDownField(
                          title: "Master Task",
                          hintText: "select master task",
                          initialValue: state.masterTaskId,
                          validatorText: "Enter Master Task",

                          items:
                              context
                                  .watch<MasterTaskCubit>()
                                  .state
                                  .masterTask
                                  .map((task) {
                                    return DropdownMenuItem<String>(
                                      value: task.docId,
                                      child: Text(task.title),
                                    );
                                  })
                                  .toList(),
                          onChanged: (value) {
                            final masterTask = context
                                .read<MasterTaskCubit>()
                                .state
                                .masterTask
                                .firstWhere((task) => task.docId == value);
                            taskFormCubit.selectMasterTask(
                              masterTask,
                              context,
                              widget.projectId,
                            );
                          },
                        ),

                        // Divider(thickness: 1, color: Colors.grey),
                        CustomTextField(
                          controller: taskFormCubit.titleController,
                          hintText: "task name",
                          title: "Title *",
                        ),

                        CustomTextField(
                          controller: taskFormCubit.descController,
                          hintText: "task desc",
                          title: "Description *",
                        ),
                        BlocBuilder<UserCubit, UserState>(
                          builder: (context, userState) {
                            if (userState.users.isNotEmpty) {
                              final projectUsers =
                                  context
                                      .read<ProjectCubit>()
                                      .fetchProjectById(widget.projectId)
                                      ?.userId ??
                                  [];

                              final users =
                                  userState.users.where((user) {
                                    return projectUsers.contains(user.docId) ||
                                        user.role == 'admin';
                                  }).toList();

                              return CustomDropDownField(
                                title: "Assign Users *",
                                hintText: "Assign to",
                                initialValue:
                                    state.selectedUser.isEmpty
                                        ? null
                                        : state.selectedUser,
                                validatorText: "Please select a user",
                                items:
                                    users.map((user) {
                                      return DropdownMenuItem<String>(
                                        value: user.docId,
                                        child: Text(user.name),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    taskFormCubit.updateSelectedUser(value);
                                  }
                                },
                              );
                            } else {
                              return const Text("No users available");
                            }
                          },
                        ),

                        CustomDateField(
                          hintText:
                              state.startDate != null
                                  ? state.startDate?.goodDayDate()
                                  : "start date",
                          title: "Start Date *",
                          onTap: () {
                            taskFormCubit.selectStartDate(context);
                          },
                          initialValue: state.startDate?.goodDayDate() ?? "",
                          validator: true,
                        ),

                        CustomDateField(
                          hintText:
                              state.endDate != null
                                  ? state.endDate?.goodDayDate()
                                  : "end date",
                          title: "End Date *",
                          onTap: () {
                            taskFormCubit.selectEndtDate(context);
                          },
                          initialValue: state.endDate?.goodDayDate() ?? "",
                          validator: true,
                        ),

                        CustomDropDownField(
                          title: "Status *",
                          hintText: "task status",
                          initialValue: state.taskStatus,
                          validator: true,
                          validatorText: "Enter Task Status",
                          items: const [
                            DropdownMenuItem(
                              value: TaskStatus.pending,
                              child: Text(TaskStatus.pending),
                            ),
                            DropdownMenuItem(
                              value: TaskStatus.ongoing,
                              child: Text(TaskStatus.ongoing),
                            ),
                            DropdownMenuItem(
                              value: TaskStatus.submitted,
                              child: Text(TaskStatus.submitted),
                            ),
                            DropdownMenuItem(
                              value: TaskStatus.approved,
                              child: Text(TaskStatus.approved),
                            ),
                          ],
                          onChanged: (value) {
                            taskFormCubit.selectStatus(value ?? "");
                          },
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Priority Status"),
                            SizedBox(height: 10),
                            Row(
                              children: [
                                FilterContainer(
                                  title: "Mid",
                                  onFilterTap: () {
                                    taskFormCubit.priorityStatus(
                                      TaskSPrioritytatus.mid,
                                    );
                                  },
                                  isSelected:
                                      state.priorityStatus ==
                                      TaskSPrioritytatus.mid,
                                ),
                                SizedBox(width: 10),
                                FilterContainer(
                                  title: "High",
                                  onFilterTap: () {
                                    taskFormCubit.priorityStatus(
                                      TaskSPrioritytatus.high,
                                    );
                                  },
                                  isSelected:
                                      state.priorityStatus ==
                                      TaskSPrioritytatus.high,
                                ),
                                SizedBox(width: 10),
                                FilterContainer(
                                  title: "Low",
                                  onFilterTap: () {
                                    taskFormCubit.priorityStatus(
                                      TaskSPrioritytatus.low,
                                    );
                                  },
                                  isSelected:
                                      state.priorityStatus ==
                                      TaskSPrioritytatus.low,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: 15),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("Attachments"),
                        SizedBox(height: 10),
                        state.selectedFile == null && state.dbFile == null
                            ? Row(
                              children: [
                                Expanded(
                                  child: CustomOutlineFileUploadField(
                                    hintText: "Choose File",
                                    onTap: () async {
                                      taskFormCubit.pickFile(context);
                                    },
                                    prefixIcon: Icon(Icons.attach_file),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Text("OR"),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: CustomOutlineFileUploadField(
                                    hintText: "Capture photo",
                                    onTap: () async {
                                      taskFormCubit.pickFileFromCamera(context);
                                    },
                                    prefixIcon: Icon(
                                      CupertinoIcons.camera,
                                      // color: AppColors.primary,
                                    ),
                                  ),
                                ),
                              ],
                            )
                            : Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CustomOutlineFileUploadField(
                                        hintText: "Choose File",
                                        onTap: () async {
                                          taskFormCubit.pickFile(context);
                                        },
                                        prefixIcon: Icon(Icons.attach_file),
                                      ),
                                      const SizedBox(height: 3),
                                      Text("OR"),
                                      const SizedBox(height: 3),
                                      CustomOutlineFileUploadField(
                                        hintText: "Capture photo",
                                        onTap: () async {
                                          taskFormCubit.pickFileFromCamera(
                                            context,
                                          );
                                        },
                                        prefixIcon: Icon(
                                          CupertinoIcons.camera,
                                          // color: AppColors.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 20),
                                Center(
                                  child: buildFilePreview(
                                    context: context,
                                    selectedFile: state.selectedFile,
                                    dbFile: state.dbFile,
                                    dbFileExt: widget.editTask?.attachmentType,
                                    dbFileName: widget.editTask?.attachmentName,
                                    isEdit: true,
                                    onDelete: () {
                                      final isDbFile =
                                          state.selectedFile == null &&
                                          state.dbFile != null;
                                      context
                                          .read<TaskFormCubit>()
                                          .deletPickFile(isDbFile);
                                    },
                                    onView: () {
                                      context
                                          .read<TaskFormCubit>()
                                          .viewPickFile(
                                            state.dbFile,
                                            context,
                                            widget.editTask?.attachmentType,
                                          );
                                    },
                                    isMessage: false,
                                  ),
                                ),
                              ],
                            ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
