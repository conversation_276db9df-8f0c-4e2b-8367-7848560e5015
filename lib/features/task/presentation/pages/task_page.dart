import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/task/presentation/widgets/task_detail_tile.dart';
import 'package:cp_associates/features/task/presentation/widgets/task_form.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TaskPage extends StatefulWidget {
  String projectId;
  TaskPage({super.key, required this.projectId});

  @override
  State<TaskPage> createState() => _TaskPageState();
}

class _TaskPageState extends State<TaskPage> {
  @override
  void initState() {
    context.read<TaskCubit>().fetchAllTask(widget.projectId);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final taskCubit = context.read<TaskCubit>();
    return BlocConsumer<TaskCubit, TaskState>(
      listener: (context, state) {},
      builder: (context, state) {
        return ResponsiveCustomBuilder(
          mobileBuilder: (width) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,

              children: [
                SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  scrollDirection: Axis.horizontal,
                  child: filterTabs(taskCubit, state),
                ),
                SizedBox(height: 20),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
                    // padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),(15),
                    child: Stack(
                      children: [
                        TaskDetailTile(projectId: widget.projectId),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: AddBtn(
                              text: "upload",
                              onPressed: () {
                                if (!context
                                    .read<ProjectCubit>()
                                    .state
                                    .haveAccess) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        "You are not authorized to perform this action.",
                                      ),
                                    ),
                                  );
                                  return;
                                }
                                showModalBottomSheet(
                                  useSafeArea: true,
                                  isScrollControlled: true,

                                  context: context,
                                  builder: (context) {
                                    return Padding(
                                      padding:
                                          MediaQuery.of(context).viewInsets,
                                      child: BlocProvider(
                                        create:
                                            (context) => TaskFormCubit(
                                              taskCubit.taskRepo,
                                            ),
                                        child: TaskForm(
                                          isMobile: true,
                                          projectId: widget.projectId,
                                          editTask: null,
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              color: AppColors.taskBtn,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Spacer(),
              ],
            );
          },

          desktopBuilder: (width) {
            return Column(
              children: [
                filterTabs(taskCubit, state),
                SizedBox(height: 10),
                Expanded(
                  child: Stack(
                    children: [
                      TaskDetailTile(projectId: widget.projectId),

                      Align(
                        alignment: Alignment.bottomRight,
                        child: AddBtn(
                          text: "upload",
                          onPressed: () {
                            if (!context
                                .read<ProjectCubit>()
                                .state
                                .haveAccess) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    "You are not authorized to perform this action.",
                                  ),
                                ),
                              );
                              return;
                            }
                            showDialog(
                              context: context,
                              builder: (context) {
                                return Dialog(
                                  child: Container(
                                    width: 800,
                                    child: BlocProvider(
                                      create:
                                          (context) =>
                                              TaskFormCubit(taskCubit.taskRepo),
                                      child: TaskForm(
                                        isMobile: false,
                                        projectId: widget.projectId,
                                        editTask: null,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          color: AppColors.taskBtn,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Row filterTabs(TaskCubit taskCubit, TaskState state) {
    return Row(
      children: [
        FilterContainer(
          title: "All",
          onFilterTap: () {
            taskCubit.filterTaskByType(TaskTypes.All);
          },
          isSelected: state.selectedType == TaskTypes.All,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "My Task",
          onFilterTap: () {
            taskCubit.filterTaskByType(TaskTypes.mytask);
          },
          isSelected: state.selectedType == TaskTypes.mytask,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Ongoing",
          onFilterTap: () {
            taskCubit.filterTaskByType(TaskTypes.onGoing);
          },
          isSelected: state.selectedType == TaskTypes.onGoing,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Submitted",
          onFilterTap: () {
            taskCubit.filterTaskByType(TaskTypes.submitted);
          },
          isSelected: state.selectedType == TaskTypes.submitted,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Completed",
          onFilterTap: () {
            taskCubit.filterTaskByType(TaskTypes.approved);
          },
          isSelected: state.selectedType == TaskTypes.approved,
        ),
      ],
    );
  }
}
