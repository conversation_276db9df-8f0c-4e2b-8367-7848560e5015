import 'dart:async';
import 'package:cp_associates/app.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/domain/repo/auth_repo.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/router.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepo authRepo;

  // Stream
  StreamSubscription<bool>? authStream;
  StreamSubscription<UserModel?>? currentUserStream;

  // Current user
  UserModel? _currentUser;
  bool get isDeleted => currentUser?.deleted ?? false;

  AuthCubit({required this.authRepo}) : super(AuthState.initial());

  //form key
  final formKey = GlobalKey<FormState>();

  void checkAuth(BuildContext context) async {
    emit(state.copyWith(loading: true, message: ""));
    authStream = authRepo.authStateStream().listen((isLoggedIn) {
      print("authStream");
      if (isLoggedIn) {
        print("authStream2");
        checkUser();
        if (!kIsWeb) {
          isTokenRefreshed();
          getDeviceTokenAndAssignToUser(context);
        }
      }
      emit(
        state.copyWith(
          isAuthenticated: isLoggedIn,
          loading: false,
          message: "",
        ),
      );
    });
  }

  void checkUser() async {
    emit(state.copyWith(loading: true, message: ""));
    currentUserStream = authRepo.currentUserStream().listen((user) {
      _currentUser = user;
      print("currentUserStream");
      emit(
        state.copyWith(
          currentUser: user,
          isDeleted: user?.deleted ?? false,
          isAdmin: user?.role == "admin",
          loading: false,
          message: "",
        ),
      );
    });
  }

  void getDeviceTokenAndAssignToUser(BuildContext context) async {
    try {
      String? token = await FBMessaging.messaging.getToken();
      if (token != null) {
        print("Device token----------: $token");
        await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
          'token': token,
        });
      }
      // rootScaffoldMessengerKey.currentState?.showSnackBar(
      //   SnackBar(content: Text("Device token assigned: $token")),
      // );
      // ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("Device token assigned: $token")));
    } catch (e) {
      print("Device token error: $e");
      // rootScaffoldMessengerKey.currentState?.showSnackBar(
      //   SnackBar(content: Text("Device token assigned: $e")),
      // );
    }
  }

  void isTokenRefreshed() {
    FBMessaging.messaging.onTokenRefresh.listen((event) {
      print("Token refreshed: $event");
    });
  }

  //register with email and password
  Future<void> register(
    String email,
    String password,
    String name,
    String role,
  ) async {
    try {
      emit(state.copyWith(loading: true, message: ""));
      await authRepo.registerWithEmailPassword(
        name,
        email.trim(),
        password,
        role,
      );
      emit(
        state.copyWith(
          message: "New user register sucessfully",
          isRegistered: true,
          loading: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          message: e.toString(),
          isRegistered: false,
          loading: false,
        ),
      );
    }
  }

  //login with email and password
  Future<void> login(String email, String password) async {
    try {
      emit(state.copyWith(loading: true, message: ""));

      final user = await authRepo.loginWithEmailPassword(
        email.toLowerCase().trim(),
        password.trim(),
      );
      if (user != null) {
        _currentUser = user;
        if (user.deleted ?? false) {
          await authRepo.logout();
          emit(
            state.copyWith(
              currentUser: null,
              isAuthenticated: false,
              loading: false,
              message: "Your account has been deleted",
            ),
          );
          return;
        }
        emit(
          state.copyWith(
            isAdmin: user.role == "admin" ? true : false,
            loading: false,
            currentUser: user,
            isAuthenticated: true,
            message: "loggedin sucessfully",
          ),
        );
      } else {
        emit(
          state.copyWith(
            currentUser: null,
            isAuthenticated: false,
            loading: false,
            message: "login failed",
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isAuthenticated: false,
          message: e.toString(),
          loading: false,
        ),
      );
    }
  }

  //forget password
  Future<void> forgetPassword(String email) async {
    emit(state.copyWith(loading: true, message: ""));
    try {
      await authRepo.forgetPassword(email);
      emit(
        state.copyWith(message: "Password reset email sent", loading: false),
      );
    } catch (e) {
      emit(state.copyWith(message: e.toString(), loading: false));
    }
  }

  //get current
  UserModel? get currentUser => _currentUser;

  //logout
  Future<void> logOut(BuildContext context) async {
    emit(state.copyWith(loading: true, message: ""));
    // Cancel the streams
    await authStream?.cancel();
    await currentUserStream?.cancel();
    await authRepo.logout();
    emit(
      state.copyWith(
        isAuthenticated: false,
        loading: false,
        message: " Logged out successfully ",
      ),
    );
    context.go(Routes.login);
  }

  @override
  Future<void> close() {
    authStream?.cancel();
    return super.close();
  }
}
