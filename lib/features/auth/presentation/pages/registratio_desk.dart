import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/auth/presentation/widgets/auth_textfield.dart';
import 'package:flutter/material.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class RegistrationDesk extends StatefulWidget {
  const RegistrationDesk({super.key});

  @override
  State<RegistrationDesk> createState() => _RegistrationDeskState();
}

class _RegistrationDeskState extends State<RegistrationDesk> {
  //Controller
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  bool isPasswordVisible = false;
  String? selectedRole;
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state.message.isNotEmpty && state.isRegistered) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
          Future.delayed(Duration(seconds: 1), () {
            context.go(Routes.home);
          });
        } else if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return IgnorePointer(
          ignoring: state.loading,
          child: Form(
            key: context.read<AuthCubit>().formKey,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              // padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Welcome text
                  Row(
                    children: [
                      Text(
                        "Register a New User",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Spacer(),
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(Icons.close),
                      ),
                    ],
                  ),

                  // Text("Register a New User", style: AppTextStyles.formtitle),
                  const SizedBox(height: 30),

                  // Name TextField
                  Container(
                    width: 430,
                    child: AuthTextField(
                      obscureText: false,
                      controller: nameController,
                      hintText: "Name",
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Email TextField
                  Container(
                    width: 430,
                    child: AuthTextField(
                      obscureText: false,
                      controller: emailController,
                      hintText: "Email",
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Password TextField
                  Container(
                    width: 430,
                    child: AuthTextField(
                      obscureText: isPasswordVisible,
                      controller: passwordController,
                      hintText: "Password",
                      icon: IconButton(
                        onPressed: () {
                          setState(() {
                            isPasswordVisible = !isPasswordVisible;
                          });
                        },
                        icon: Icon(
                          isPasswordVisible
                              ? Icons.visibility_off_outlined
                              : Icons.visibility_outlined,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Container(
                    width: 430,
                    // padding: EdgeInsets.symmetric(horizontal: 16),
                    child: DropdownButtonFormField(
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.grey2),
                        ),
                        hintText: "Role",
                        hintStyle: AppTextStyles.hintText,

                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: Role.designer,
                          child: Text(Role.designer),
                        ),
                        DropdownMenuItem(
                          value: Role.supervisor,
                          child: Text(Role.supervisor),
                        ),
                      ],
                      onChanged: (value) {
                        selectedRole = value;
                      },
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Login button
                  PrimaryButton(
                    width: 430,
                    text: "REGISTER",
                    onPressed: () {
                      if (context
                          .read<AuthCubit>()
                          .formKey
                          .currentState!
                          .validate()) {
                        context.read<AuthCubit>().register(
                          emailController.text,
                          passwordController.text,
                          nameController.text,
                          selectedRole ?? "",
                        );
                      }
                    },
                    isLoading: state.loading,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
