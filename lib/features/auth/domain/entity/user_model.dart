import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String docId;
  final String name;
  final String email;
  final String role;
  final bool? deleted;
  final String? token;
  final List<String>? topics;

  UserModel({
    required this.docId,
    required this.name,
    required this.email,
    required this.role,
    this.deleted,
    this.token,
    this.topics,
  });

  /// Create a User from Firestore snapshot
  factory UserModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return UserModel(
      docId: snap.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      role: data['role'] ?? '',
      deleted: data.containsKey('deleted') ? data['deleted'] as bool? : null,
      token: data['token'] ?? null,
      topics:
          data.containsKey('tokens') ? List<String>.from(data['tokens']) : null,
    );
  }

  /// Create a User from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      docId: json['docId'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? '',
      deleted: json.containsKey('deleted') ? json['deleted'] as bool? : null,
      token: json['token'] ?? null,
      topics:
          json.containsKey('tokens') ? List<String>.from(json['tokens']) : null,
    );
  }

  /// Convert User to Map for Firestore or other storage
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'role': role,
      if (deleted != null) 'deleted': deleted,
      if (token != null) 'deviceToken': token,
      if (topics != null) 'tokens': topics,
    };
  }

  /// Convert User to JSON (include docId if needed)
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'role': role,
      if (deleted != null) 'deleted': deleted,
      if (token != null) 'token': token,
      if (topics != null) 'tokens': topics,
    };
  }
}
