import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/home/<USER>/antity/setting_model.dart';
import 'package:cp_associates/features/home/<USER>/repo/setting_repo.dart';

class SettingFirebaseRepo implements SettingRepo {
  final settingsRef = FBFireStore.settings;

  @override
  Stream<SettingsModel?> getSettings() {
    return settingsRef.snapshots().map((snapshot) {
      if (!snapshot.exists) return null;
      return SettingsModel.fromSnapshot(snapshot);
    });
  }

  @override
  Future<void> checkVersionSupported() async {
    
  }
}
