import 'package:cloud_firestore/cloud_firestore.dart';

class SettingsModel {
  final String docId;
  final String version;
  final String andbuildNumber;
  final String iosbuildNumber;

  SettingsModel({
    required this.docId,
    required this.version,
    required this.andbuildNumber,
    required this.iosbuildNumber,
  });

  SettingsModel copyWith({
    String? docId,
    String? version,
    String? andbuildNumber,
    String? iosbuildNumber,
  }) {
    return SettingsModel(
      docId: docId ?? this.docId,
      version: version ?? this.version,
      andbuildNumber: andbuildNumber ?? this.andbuildNumber,
      iosbuildNumber: iosbuildNumber ?? this.iosbuildNumber,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'version': version,
      'andbuildNumber': andbuildNumber,
      'iosbuildNumber': iosbuildNumber,
    };
  }

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      docId: json['docId'] ?? '',
      version: json['version'] ?? '',
      andbuildNumber: json['andbuildNumber'] ?? '',
      iosbuildNumber: json['iosbuildNumber'] ?? '',
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'andbuildNumber': andbuildNumber,
      'iosbuildNumber': iosbuildNumber,
    };
  }

  factory SettingsModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return SettingsModel.fromJson({
      ...data,
      'docId': snapshot.id, // override docId from document ID
    });
  }
}

// create from snap
