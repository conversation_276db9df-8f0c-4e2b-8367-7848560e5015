part of 'setting_cubit.dart';

class SettingState {
  final SettingsModel? settings;
  final bool versionSupported;
  final bool hasInternetConnection;

  SettingState({
    required this.settings,
    required this.versionSupported,
    required this.hasInternetConnection,
  });

  factory SettingState.initial() {
    return SettingState(
      settings: null,
      versionSupported: false,
      hasInternetConnection: true, // Assume connected initially
    );
  }

  SettingState copyWith({
    SettingsModel? settings,
    bool? versionSupported,
    bool? hasInternetConnection,
  }) {
    return SettingState(
      settings: settings ?? this.settings,
      versionSupported: versionSupported ?? this.versionSupported,
      hasInternetConnection:
          hasInternetConnection ?? this.hasInternetConnection,
    );
  }
}
