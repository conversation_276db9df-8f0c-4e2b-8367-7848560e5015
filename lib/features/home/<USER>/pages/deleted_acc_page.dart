import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class DeletedAccountPage extends StatelessWidget {
  const DeletedAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Rocket image
                Icon(
                  CupertinoIcons.delete_simple,
                  size: 180,
                  color: const Color.fromARGB(255, 229, 99, 99),
                ),

                const SizedBox(height: 40),

                // Title
                const Text(
                  "Account Deleted",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                const SizedBox(height: 12),

                // Subtitle
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    "Your account has been deleted. Please contact admin for more information.",

                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                      height: 1.5,
                    ),
                  ),
                ),

                const SizedBox(height: 30),

                // Update now button
                SizedBox(
                  width: 220,
                  child: ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: TransparentInkWell(
                      onTap: () async {
                        await FBAuth.auth.signOut();
                        context.go(Routes.login);
                      },
                      child: const Text(
                        "Back to Login",
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Close the app link
                TextButton(
                  onPressed: () {},
                  child: const Text(
                    "Contact Admin",
                    style: TextStyle(fontSize: 14, color: AppColors.black),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
