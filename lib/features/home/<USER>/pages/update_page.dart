import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

class UpdateScreen extends StatelessWidget {
  const UpdateScreen({super.key});

  void _openAppStore() {
    // For now, just show a message. In a real app, you would open the app store
    // You can implement platform-specific store URLs here
    if (Platform.isAndroid) {
      // Open Google Play Store
      // launch('https://play.google.com/store/apps/details?id=your.package.name');
    } else if (Platform.isIOS) {
      // Open App Store
      // launch('https://apps.apple.com/app/your-app-id');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Rocket image
                Image.asset("assets/images/update.png", height: 180),
                const SizedBox(height: 40),

                // Title
                const Text(
                  "New update is available",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                const SizedBox(height: 12),

                // Subtitle
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    "The current version of this application is no longer supported. "
                    "We apologize for any inconvenience we may have caused you.",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                      height: 1.5,
                    ),
                  ),
                ),

                const SizedBox(height: 30),

                // Update now button
                SizedBox(
                  width: 220,
                  child: ElevatedButton(
                    onPressed: () {
                      // Open app store or play store for update
                      _openAppStore();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: const Text(
                      "Update now",
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Close the app link
                TextButton(
                  onPressed: () {
                    // Close the app
                    SystemNavigator.pop();
                  },
                  child: const Text(
                    "No, Thanks! Close the app",
                    style: TextStyle(fontSize: 14, color: AppColors.black),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
